<?php

use App\Http\Controllers\DiscussionController;
use App\Http\Controllers\FinancesController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\InvitationController;
use App\Http\Controllers\IssueController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\ProposalController;
use App\Http\Controllers\RemindersController;
use App\Http\Controllers\SessionController;
use App\Http\Middleware\CheckSuspended;
use App\Http\Middleware\Validated;
use App\Http\Controllers\UserController;
use App\Models\User;
use App\Models\LocationHelper;
use Illuminate\Support\Facades\Route;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\View;
use Illuminate\Http\Request;
use Monarobase\CountryList\CountryListFacade as Countries;

// Temporary route for authentication testing
// Route::get('/_test_auth', function (Request $request) {
//     $credentials = [
//         'email' => '<EMAIL>',
//         'password' => 'B1smill@h2349'
//     ];
//     if (Auth::attempt($credentials)) {
//         return response()->json(['status' => 'success', 'user' => Auth::user()->email]);
//     } else {
//         return response()->json(['status' => 'failure']);
//     }
// });
// End temporary route

// New temporary route for detailed authentication debugging
// Route::get('/_test_auth_debug', function (Request $request) { // Commenting out again
//     $email = '<EMAIL>';
//     $password = '6666billah'; 
//     $user = User::where('email', $email)->first();
// 
//     if (!$user) {
//         return response()->json(['status' => 'user_not_found']);
//     }
//     
//     // Also return the hashed password from DB for inspection
//     if (Hash::check($password, $user->password)) {
//         return response()->json(['status' => 'password_valid', 'user_id' => $user->id, 'user_email' => $user->email, 'db_hash' => $user->password]);
//     } else {
//         return response()->json(['status' => 'password_invalid', 'user_id' => $user->id, 'user_email' => $user->email, 'db_hash' => $user->password]);
//     }
// }); // Commenting out again
// End new temporary route

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/', function () {
//     return view('welcome');
// });

// DELETE ME!!!  Just playing around w/ countries:

Route::get('/countries', function () {
    // Updated to use monarobase/country-list
    return Countries::getList('en');

    // Or for the select list
    return LocationHelper::countriesForSelect();
});

// Default route redirects based on authentication status
Route::get('/', function () {
    // Log the root route access for debugging
    \Log::info('Root route accessed', [
        'authenticated' => Auth::check(),
        'user_id' => Auth::check() ? Auth::id() : null
    ]);

    if (Auth::check()) {
        return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
    }
    return redirect()->route('auth.login');
});

// New modern signup page
Route::get('/signup', function () {
    return view('landing');
})->name('signup');

// Register routes for the new flow
Route::post('/register/start', [UserController::class, 'registerStart'])->name('register.start');
Route::get('/register/start', [UserController::class, 'showRegisterStart'])->name('register.start.show');
Route::post('/register/complete', [UserController::class, 'completeRegistration'])->name('register.complete');

// Removed social login routes from here and moved them to the bottom of the file

Route::get('group_token/{token}', [InvitationController::class, 'redeem_token'])->name('auth.group_token');

Route::get('resources', function () {
    return View::make('resources');
});
Route::get('help', function () {
    return View::make('help');
});

Route::get('login.php', function () {
    return redirect()->route('auth.login');
});

// ===== AUTHENTICATION ROUTES =====
// These routes are defined outside of any middleware to ensure they're always accessible

// // Main login routes - consolidated
// Route::get('/login', [SessionController::class, 'create'])->name('login');
// Route::post('/login', [SessionController::class, 'store'])->name('login.post');

// Auth login routes - handle both GET and POST
Route::get('/auth/login', [SessionController::class, 'create'])->name('auth.login');
Route::post('/auth/login', [SessionController::class, 'store'])->name('auth.login.post');

// Redirect old login routes to the main login route
Route::get('/login-direct', function() {
    return redirect()->route('auth.login');
});

Route::get('/signin', function() {
    return redirect()->route('auth.login');
});

// Remove redundant login.php route
// Route::get('login.php', function () {
//     return redirect()->route('auth.login');
// });

// Simple test route
Route::get('/test-route', function() {
    return 'This is a test route that should always work';
})->middleware('web');

// Only do these routes for people not logged in, otherwise send them to /home  (see filters.php)
Route::middleware(['web', 'guest'])->group(function () {
    // Other guest routes
    Route::post('register', [UserController::class, 'store']);

    Route::get('password/remind', [RemindersController::class, 'getRemind']);
    Route::post('password/remind', [RemindersController::class, 'postRemind'])->name('password.remind');
    Route::get('password/reset/{token}', [RemindersController::class, 'getReset'])->name('password.getreset');
    Route::post('password/reset', [RemindersController::class, 'postReset'])->name('password.reset');

    Route::get('email-confirmed', [UserController::class, 'showEmailConfirmed'])->name('email.confirmed');
    Route::get('create-password', [UserController::class, 'showCreatePassword'])->name('password.create.show');
    Route::post('create-password', [UserController::class, 'createPassword'])->name('password.create');
});

// Email verification routes
Route::middleware('guest')->group(function () {
    Route::post('register/send-otp', [UserController::class, 'sendOtp'])->name('register.send-otp');
    Route::get('verify-email/{email}', [UserController::class, 'showVerifyEmail'])->name('email.verify.show');
    Route::post('verify-email', [UserController::class, 'verifyEmail'])->name('email.verify');
    Route::get('resend-verification', [UserController::class, 'resendVerification'])->name('email.resend');
});

// Only do these routes for people who are logged in, otherwise send them to /login  (see filters.php)
Route::middleware('auth')->group(function () {
    // Logout routes
    Route::post('/logout', [SessionController::class, 'destroy'])->name('logout');
    Route::get('/logout', [SessionController::class, 'destroy'])->name('logout.get');
    
    Route::get('stop_impersonate', [SessionController::class, 'stop_impersonating'])->name('stop_impersonate');

    Route::get('suspended', function () {
        return View::make('suspended');
    });

    Route::middleware(CheckSuspended::class)->group(function () {
        Route::get('sendvalidation', [UserController::class, 'sendValidation']);
        Route::get('validate', function () {
            if (Auth::User()->email_valid) {
                return redirect('/wizard');
            } else {
                return View::make('validate');
            }
        });
        Route::get('validate/{token}', [UserController::class, 'confirmValidation']);

        // Only email-validated users can hit this routes, otherwise send them to /validate  (see filters.php)
        Route::middleware(Validated::class)->group(function () {
            Route::middleware('admin')->group(function () {
                Route::get('admin', function () {
                    return redirect()->route('group.admin_index');
                });

                Route::get('admin/groups', [GroupController::class, 'admin_index'])->name('group.admin_index');
                Route::get('admin/group/{id}', [GroupController::class, 'site_admin'])->name('group.site_admin');
                Route::put('admin/group/{id}/update', [GroupController::class, 'admin_update'])->name('group.admin_update');

                Route::get('admin/issues', [IssueController::class, 'admin_index'])->name('issue.admin_index');
                Route::get('admin/issue/{id}', [IssueController::class, 'site_admin'])->name('issue.site_admin');
                Route::get('admin/issue/{id}/delete', [IssueController::class, 'admin_delete'])->name('issue.admin_delete');
                Route::get('admin/issue/{id}/restore', [IssueController::class, 'admin_restore'])->name('issue.admin_restore');

                Route::get('admin/users', [UserController::class, 'admin_index'])->name('user.admin_index');
                Route::get('admin/user/{id}/impersonate', [SessionController::class, 'impersonate'])->name('user.impersonate');
                Route::get('admin/user/{id}/toggle_suspended', [UserController::class, 'toggleSuspended'])->name('user.toggle_suspended');
                Route::get('admin/user/{id}/toggle_mobile', [UserController::class, 'toggleMobile'])->name('user.toggle_mobile');
                Route::get('admin/user/{id}/validate_email', [UserController::class, 'manualValidation'])->name('user.validate_email');
                Route::get('admin/user/{id}/reset_link', [RemindersController::class, 'resetLink'])->name('user.reset_link');
                Route::get('admin/group/{id}/toggle_suspended', [GroupController::class, 'toggleSuspended'])->name('group.toggle_suspended');
            });

            Route::get('buttons', function () {
                return View::make('buttons');
            });

            Route::get('home', [GroupController::class, 'group_home'])->name('home');

            Route::get('wizard', function () {
                return redirect()->route(Auth::User()->currentWizardRoute());
            })->name('wizard');
            Route::get('wizard/account', [UserController::class, 'edit_wizard'])->name('wizard.account');
            Route::post('wizard/account', [UserController::class, 'update_wizard']);

            Route::get('wizard/group', [GroupController::class, 'wizard_index'])->name('wizard.group');
            Route::get('wizard/group/create', [GroupController::class, 'wizard_create']);
            Route::post('wizard/group/create', [GroupController::class, 'wizard_store']);
            Route::post('wizard/group/join/{group}', [GroupController::class, 'wizard_join'])->name('wizard.join');
            Route::get('wizard/donation', [FinancesController::class, 'wizard_edit'])->name('wizard.donation');
            Route::post('wizard/donate', [FinancesController::class, 'wizard_donate'])->name('wizard.donate');
            Route::get('wizard/{section}/finish', function ($section) {
                $user = Auth::User();
                $user->finishedWizardSection($section);
                $user->save();
                $destination = redirect()->route($user->currentWizardRoute());
                if ($user->currentWizardRoute() == 'home') {
                    return $destination->withAlerts(['success' => "You've completed the Get Started wizard!"]);
                }

                return $destination;
            })->name('wizard.finish');

            Route::get('wizard/intro', [DiscussionController::class, 'wizard_create'])->name('wizard.intro');
            Route::post('wizard/intro', [DiscussionController::class, 'wizard_store']);

            Route::get('account', function () {
                return redirect()->route('account.profile');
            });

            Route::prefix('account')->group(function () {
                Route::get('profile', [UserController::class, 'edit'])->name('account.profile');
                Route::get('profile/edit', [UserController::class, 'edit'])->name('account.profile.edit');
                Route::put('profile', [UserController::class, 'update'])->name('account.profile.update');
                Route::get('invite', [UserController::class, 'invitePage'])->name('invite.create');
                Route::post('invite', [UserController::class, 'sendInvite'])->name('invite.send');
                Route::get('default_group/{groupid}', [UserController::class, 'setDefaultGroup'])
                    ->where('groupid', '[0-9]+') // Constraint for groupid
                    ->name('user.set_default_group');
                Route::get('finances', [FinancesController::class, 'edit'])->name('account.finances');
                Route::get('uk_paypal_common_change', [FinancesController::class, 'uk_paypal_common_change'])->name('account.finances_uk_common_change');
                Route::get('uk_paypal_group', [FinancesController::class, 'uk_paypal_group'])->name('account.finances_uk_group');
                Route::post('mobile', [UserController::class, 'set_mobile'])->name('account.mobile');
            });

            Route::post('finances/donate', [FinancesController::class, 'donate'])->name('finances.donate');

            Route::get('decline_invite/{groupid}', [InvitationController::class, 'decline_invite'])->name('invite.decline');

            Route::resource('user', UserController::class)->only(['index', 'store', 'show']);

            Route::resource('discussion', DiscussionController::class);
            Route::get('discussion/create', [DiscussionController::class, 'default_create'])->name('discussion.create');
            Route::post('discussion/{discussion}/post', [DiscussionController::class, 'post'])->name('discussion.post');
            Route::get('discussion/{discussion}/new_issue', [DiscussionController::class, 'new_issue'])->name('discussion.new_issue');
            Route::get('discussion/{discussion}/delete', [DiscussionController::class, 'destroy'])->name('discussion.delete');

            Route::resource('issue', IssueController::class);
            Route::get('issue/create', [IssueController::class, 'default_create'])->name('issue.create');
            Route::post('issue/confirm', [IssueController::class, 'confirm'])->name('issue.confirm');
            Route::post('issue/{issue}/post', [IssueController::class, 'post'])->name('issue.post');
            Route::post('issue/{issue}/admin_update', [IssueController::class, 'admin_update'])->name('issue.admin_update');

            Route::resource('post', PostController::class);
            Route::get('post/{post}/delete', [PostController::class, 'destroy'])->name('post.delete');

            Route::resource('group', GroupController::class);
            Route::post('group/{id}/join', [GroupController::class, 'join'])->name('group.join');
            Route::get('group/{id}/leave', [GroupController::class, 'leave'])->name('group.leave');

            Route::get('group/{id}/issues', [IssueController::class, 'forGroup'])->name('group.issues');
            Route::get('group/{id}/issue/create', [IssueController::class, 'create'])->name('group.create_issue');
            Route::get('group/{id}/discussions', [DiscussionController::class, 'forGroup'])->name('group.discussions');
            Route::get('group/{id}/discussion/create', [DiscussionController::class, 'create'])->name('group.create_discussion');

            Route::put('group/{id}/user/{userid}', [GroupController::class, 'update_member'])->name('group.update_user');
            Route::get('group/{id}/admin', function ($id) {
                return redirect()->route('group.admin_members', ['id' => $id]);
            })->name('group.admin')->name('');

            // Member admin:
            Route::get('group/{id}/admin/members', [GroupController::class, 'admin'])->name('group.admin_members');
            Route::get('group/{id}/promote/{userid}', [GroupController::class, 'toggle_admin'])->name('group.toggle_admin');
            Route::get('group/{id}/remove/{userid}', [GroupController::class, 'remove_member'])->name('group.remove');
            Route::get('group/{id}/absent/{userid}', [GroupController::class, 'toggle_absent'])->name('group.toggle_absent');

            // Join requests:
            Route::get('group/{id}/admin/join_requests', [GroupController::class, 'join_requests'])->name('group.join_requests');
            Route::get('group/{id}/approve/{userid}', [GroupController::class, 'accept_request'])->name('group.accept_request');
            Route::get('group/{id}/deny/{userid}', [GroupController::class, 'reject_request'])->name('group.reject_request');
            Route::get('group/{id}/set_current', [GroupController::class, 'set_current'])->name('group.set_current');

            Route::post('proposal/{proposal}/vote/{vote}', [ProposalController::class, 'vote'])->name('proposal.vote');
            Route::get('proposal/{proposal}/payment_info', [ProposalController::class, 'payment_info'])->name('proposal.payment_info');

            Route::get('mygroups', [GroupController::class, 'my_groups'])->name('mygroups');

            // This route is commented out because we're moving it outside the auth middleware
            // Route::get('dashboard/{user_id}', [App\Http\Controllers\DashboardController::class, 'show'])->name('dashboard.new');

            Route::put('group/{id}/update-banner', [GroupController::class, 'updateBanner'])->name('group.update-banner');

        });

    });
});

// ===== DASHBOARD ROUTES =====

// Default dashboard route that redirects to the user's dashboard
Route::get('dashboard', function() {
    if (!Auth::check()) {
        return redirect()->route('auth.login')->with('intended', route('dashboard'));
    }
    return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
})->name('dashboard');

// Main dashboard route with user_id parameter
Route::get('dashboard/{user_id}', function($user_id) {
    if (!Auth::check()) {
        return redirect()->route('auth.login')->with('intended', route('dashboard.new', ['user_id' => $user_id]));
    }

    // Check if user is trying to access another user's dashboard
    if (Auth::id() != $user_id && !Auth::user()->is_admin) {
        return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
    }

    // Show the dashboard
    return app()->make(\App\Http\Controllers\DashboardController::class)->show($user_id);
})->name('dashboard.new');

// V2 dashboard route
Route::get('dashboard/{user_id}/v2', function($user_id) {
    if (!Auth::check()) {
        return redirect()->route('auth.login')->with('intended', url('dashboard/' . $user_id . '/v2'));
    }
    // Only allow users to view their own dashboard unless they're an admin
    if (Auth::id() != $user_id && !Auth::user()->is_admin) {
        return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
    }
    return app()->make(\App\Http\Controllers\DashboardController::class)->show_v2($user_id);
})->name('dashboard.v2');

// Simple dashboard routes
Route::get('dashboard-simple/{user_id}', function($user_id) {
    // Check if user is authenticated
    if (!Auth::check()) {
        return redirect()->route('auth.login');
    }

    return app()->make(\App\Http\Controllers\DashboardSimpleController::class)->show($user_id);
})->name('dashboard.simple');

Route::get('dashboard-basic', function() {
    // Check if user is authenticated
    if (!Auth::check()) {
        return redirect()->route('auth.login');
    }

    return view('dashboard.simple');
})->name('dashboard.basic');

// Test dashboard route
Route::get('dashboard-test/{user_id}', function($user_id) {
    // This route is accessible without authentication for testing
    return app()->make(\App\Http\Controllers\DashboardController::class)->show($user_id);
})->name('dashboard.test');

// Direct dashboard access test
Route::get('direct-dashboard-test', function() {
    // Find a user (for testing purposes)
    $user = \App\Models\User::where('email', 'like', '%@%')->first();

    if (!$user) {
        return 'No users found in the database.';
    }

    // Force login
    Auth::login($user);

    // Check if login was successful
    if (Auth::check()) {
        // Set a secure cookie for HTTPS environments
        $isSecure = request()->secure() || (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on');
        $cookie = cookie('auth_user_id', $user->id, 60, '/', null, $isSecure, false);

        // Get data needed for the dashboard
        try {
            $recentDiscussions = \App\Models\Discussion::with(['group', 'user'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            $recentDiscussions = collect();
        }

        try {
            $recentIssues = \App\Models\Issue::with(['group', 'user'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            $recentIssues = collect();
        }

        try {
            $userGroups = \App\Models\Group::whereHas('members', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->get();
        } catch (\Exception $e) {
            $userGroups = collect();
        }

        // Return the dashboard view directly
        return response()
            ->view('dashboard.new', [
                'user' => $user,
                'recentDiscussions' => $recentDiscussions,
                'recentIssues' => $recentIssues,
                'userGroups' => $userGroups,
                'debug_info' => [
                    'auth_check' => Auth::check(),
                    'auth_id' => Auth::id(),
                    'session_id' => session()->getId()
                ]
            ])
            ->withCookie($cookie);
    } else {
        return response()->json([
            'success' => false,
            'message' => 'Failed to log in',
            'session_id' => session()->getId()
        ]);
    }
});

// Public view for all non-secret groups:
Route::get('grp/{id}', [GroupController::class, 'public_show_by_id'])->name('group.public_show_id');

// Route::get('testing', function() {
// 	$group = Group::with('members')->find(2);
// 	$result = '';
// 	foreach( $group->members as $member) {
// 		$result .= $member->firstname." ".$member->pivot->email_notification." ".$member->pivot->email_pref."<br />\n";
// 	}
// 	return $result;

// });

// Catch-all to show groups that have shortname set:
Route::get('groups/{path}', [GroupController::class, 'public_show_by_shortname'])->name('group.public_show_shortname');

// Fallback route to catch any undefined routes
Route::fallback(function () {
    $path = request()->path();
    \Log::warning('Fallback route accessed', [
        'path' => $path,
        'url' => request()->url(),
        'method' => request()->method(),
        'user_agent' => request()->userAgent()
    ]);

    // Special handling for login route
    if ($path === 'login') {
        return redirect()->route('auth.login');
    }

    return response()->view('errors.404', ['path' => $path], 404);
});

// Social login routes - placed outside of any middleware groups
Route::get('/sso/google', [SessionController::class, 'redirectToGoogle'])->name('login.google');
Route::get('/sso/google/callback', [SessionController::class, 'handleGoogleCallback'])->name('login.google.callback');
Route::get('/sso/apple', [SessionController::class, 'redirectToApple'])->name('login.apple');
Route::get('/sso/apple/callback', [SessionController::class, 'handleAppleCallback'])->name('login.apple.callback');
Route::get('/sso/microsoft', [SessionController::class, 'redirectToMicrosoft'])->name('login.microsoft');
Route::get('/sso/microsoft/callback', [SessionController::class, 'handleMicrosoftCallback'])->name('login.microsoft.callback');
Route::get('/sso/facebook', [SessionController::class, 'redirectToFacebook'])->name('login.facebook');
Route::get('/sso/facebook/callback', [SessionController::class, 'handleFacebookCallback'])->name('login.facebook.callback');

// Test login routes
Route::get('/login-test', function() {
    return 'Login test route works!';
});

// Direct login test route - attempts to log in a user directly
Route::get('/direct-login-test', function() {
    // Find a user (for testing purposes)
    $user = \App\Models\User::where('email', 'like', '%@%')->first();

    if (!$user) {
        return 'No users found in the database.';
    }

    // Log the attempt
    \Log::info('Direct login test', [
        'user_id' => $user->id,
        'email' => $user->email,
        'session_id' => session()->getId()
    ]);

    // Force login
    Auth::login($user);

    // Check if login was successful
    if (Auth::check()) {
        // Set a secure cookie for HTTPS environments
        $isSecure = request()->secure() || (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on');
        $cookie = cookie('auth_user_id', $user->id, 60, '/', null, $isSecure, false);

        // Return response with debug info
        return response()
            ->json([
                'success' => true,
                'message' => 'Successfully logged in',
                'user' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'name' => $user->firstname . ' ' . $user->lastname
                ],
                'auth' => [
                    'check' => Auth::check(),
                    'id' => Auth::id()
                ],
                'session' => [
                    'id' => session()->getId(),
                    'has_user_id' => session()->has('user_id')
                ],
                'request' => [
                    'secure' => request()->secure(),
                    'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on',
                    'host' => request()->getHost()
                ],
                'dashboard_url' => route('dashboard.new', ['user_id' => $user->id])
            ])
            ->withCookie($cookie);
    } else {
        return response()->json([
            'success' => false,
            'message' => 'Failed to log in',
            'session_id' => session()->getId()
        ]);
    }
});

// Debug route to show all defined routes
Route::get('/routes-debug', function() {
    $routes = collect(\Route::getRoutes())->map(function ($route) {
        return [
            'uri' => $route->uri(),
            'name' => $route->getName(),
            'methods' => implode('|', $route->methods()),
            'action' => $route->getActionName(),
        ];
    })->filter(function ($route) {
        // Filter to only show login-related routes
        return str_contains($route['uri'], 'login') ||
               str_contains($route['name'] ?? '', 'login') ||
               str_contains($route['uri'], 'auth') ||
               $route['uri'] === '/';
    });

    return response()->json($routes);
});

// Advanced debug routes to diagnose issues
Route::get('/login-debug', function() {
    return response()->json([
        'server' => $_SERVER,
        'request' => [
            'path' => request()->path(),
            'url' => request()->url(),
            'fullUrl' => request()->fullUrl(),
            'method' => request()->method(),
            'isSecure' => request()->secure(),
            'ip' => request()->ip(),
            'userAgent' => request()->userAgent(),
        ],
        'app' => [
            'environment' => app()->environment(),
            'locale' => app()->getLocale(),
            'isDownForMaintenance' => app()->isDownForMaintenance(),
        ],
        'auth' => [
            'check' => auth()->check(),
            'id' => auth()->id(),
        ],
    ]);
});

// Session debug route
Route::get('/session-debug', function() {
    return response()->json([
        'session' => [
            'id' => session()->getId(),
            'token' => csrf_token(),
            'has_user_id' => session()->has('user_id'),
            'user_id' => session('user_id'),
            'auth_checked' => session('auth_checked'),
            'all' => array_keys(session()->all()),
        ],
        'cookies' => [
            'has_auth_cookie' => request()->hasCookie('auth_user_id'),
            'auth_cookie' => request()->cookie('auth_user_id'),
            'all_cookies' => request()->cookies->all(),
        ],
        'config' => [
            'session_driver' => config('session.driver'),
            'session_lifetime' => config('session.lifetime'),
            'session_secure' => config('session.secure'),
            'session_same_site' => config('session.same_site'),
        ],
        'auth' => [
            'check' => auth()->check(),
            'id' => auth()->id(),
            'user' => auth()->check() ? auth()->user()->only(['id', 'email', 'firstname', 'lastname']) : null,
        ],
    ]);
});

// Auth debug route to check authentication status
Route::get('/auth-debug', function() {
    if (Auth::check()) {
        return response()->json([
            'authenticated' => true,
            'user' => Auth::user()->only(['id', 'email', 'firstname', 'lastname']),
            'session_id' => session()->getId(),
            'intended_url' => session()->get('url.intended'),
            'dashboard_url' => route('dashboard.new', ['user_id' => Auth::id()]),
        ]);
    } else {
        return response()->json([
            'authenticated' => false,
            'session_id' => session()->getId(),
            'intended_url' => session()->get('url.intended'),
            'login_url' => route('auth.login'),
        ]);
    }
});

// Logout debug route
Route::get('/logout-debug', function() {
    // Log before logout
    \Log::info('Logout debug route accessed', [
        'authenticated' => Auth::check(),
        'user_id' => Auth::check() ? Auth::id() : null,
        'session_id' => session()->getId()
    ]);

    // Perform logout
    Auth::logout();

    // Log after logout
    \Log::info('After logout', [
        'authenticated' => Auth::check(),
        'session_id' => session()->getId()
    ]);

    return redirect()->route('auth.login')->with('success', 'You have been logged out successfully.');
});

// SSO route
Route::get('/sso', function() {
    return view('auth.login');
})->name('sso');

// Direct logout route that bypasses middleware
Route::get('/direct-logout', function() {
    // Log before logout
    \Log::info('Direct logout route accessed', [
        'authenticated' => Auth::check(),
        'user_id' => Auth::check() ? Auth::id() : null,
        'session_id' => session()->getId()
    ]);

    // Flush the session
    session()->flush();

    // Perform logout
    Auth::logout();

    // Regenerate the session ID
    session()->regenerate(true);

    // Clear any cookies
    $response = redirect()->route('auth.login')->with('success', 'You have been logged out successfully.');

    // Remove the auth_user_id cookie
    $response->cookie('auth_user_id', '', -1);

    // Log after logout
    \Log::info('After direct logout', [
        'authenticated' => Auth::check(),
        'session_id' => session()->getId()
    ]);

    return $response;
});

// Direct login route outside of any middleware
Route::get('/auth/login', [SessionController::class, 'create'])->name('auth.login');
