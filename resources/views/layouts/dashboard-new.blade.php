<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Common Change') }} - @yield('title', 'Dashboard')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('css/dashboard-menu.css') }}">
    <link rel="stylesheet" href="{{ asset('css/dashboard-content.css') }}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">


    <style>
        /* CSS Variables for menu dimensions */
        :root {
            --main-menu-collapsed-width: 70px;
            --submenu-width: 220px;
            --primary-purple: #5144A1;
            --primary-navy: #4D5E80;
            --light-white: #FFFFFF;
            --light-lavender: #F0F0F9;
        }

        /* User dropdown menu styles */
        .user-profile {
            position: relative;
            cursor: pointer;
        }

        .user-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            width: 200px;
            background-color: var(--light-white);
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 10px 0;
            margin-top: 10px;
            z-index: 1000;
            display: none;
        }

        .user-profile.active .user-dropdown-menu {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: var(--primary-navy);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--light-lavender);
        }

        .dropdown-item i {
            width: 20px;
            margin-right: 10px;
            color: var(--primary-purple);
        }

        .dropdown-divider {
            height: 1px;
            background-color: rgba(0, 0, 0, 0.05);
            margin: 5px 0;
        }

        .ml-2 {
            margin-left: 8px;
        }

        .content-wrapper .content-body .container { /* styles */ }

        /* Add styles for expanded main nav */
        body.mygroups-route .main-menu {
            width: 250px;
        }

        body.mygroups-route .content-wrapper {
            margin-left: 250px;
            margin-left: 250px !important;
            width: calc(100% - 250px);
        }

        /* Adjust content spacing for mygroups route */
        body.mygroups-route .content-body {
            padding: 0;
            margin: 0;
        }

        body.mygroups-route .content-header {
            padding: 1rem;
            margin: 0;
        }

        /* Dashboard-style layout: collapsed main menu + expanded sub-menu */
        body.dashboard-style .content-wrapper {
            margin-left: calc(var(--main-menu-collapsed-width) + var(--submenu-width));
            width: calc(100% - var(--main-menu-collapsed-width) - var(--submenu-width));
        }

        body.dashboard-style .content-body {
            padding: 0;
            margin: 0;
        }

        body.dashboard-style .content-header {
            padding: 1rem;
            margin: 0;
        }
    </style>

    @stack('styles')
</head>
<body class="{{
    (request()->is('mygroups') || request()->is('group/create')) ? 'mygroups-route' :
    ((request()->is('discussion*') || request()->is('issue*') || request()->is('dashboard*') || request()->is('group/*/issue/*') || request()->is('group/*/issues') || request()->is('group/*')) ? 'dashboard-style' : '')
}}">
    <!-- Main Menu -->
    @include('partials.main-menu')

    <!-- Submenu - Show for dashboard-style routes, hide for mygroups routes -->
    @unless(request()->is('mygroups') || request()->is('group/create'))
        @if(request()->is('discussion*') || request()->is('issue*') || request()->is('dashboard*') || request()->is('group/*/issue/*') || request()->is('group/*/issues') || request()->is('group/*'))
            @include('partials.sub-menu')
        @endif
    @endunless

    <!-- Content Area -->
    <div class="content-wrapper">
        <div class="content-header">
            <div class="header-left">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search...">
                </div>
            </div>
            <div class="header-right">
                <div class="notification-icon">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">0</span>
                </div>
                <!-- Direct logout link for testing -->
                <!-- Removed direct logout link and logout form button as per Figma, keeping only dropdown logout -->
                <!-- <a href="{{ url('/logout-debug') }}" style="margin-right: 15px; color: var(--primary-purple); text-decoration: none;">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a> -->

                <!-- <form action="{{ url('/logout') }}" method="POST" style="display: inline-block; margin-right: 15px;">
                    @csrf
                    <button type="submit" style="background: none; border: none; color: var(--primary-purple); cursor: pointer;">
                        <i class="fas fa-power-off"></i> Logout (Form)
                    </button>
                </form> -->
                <div class="user-profile" id="userProfileDropdown">
                    <div class="user-avatar">
                        <img src="{{ asset('images/profile-placeholder.png') }}" alt="User Avatar">
                    </div>
                    <span class="user-name">
                        @if(Auth::check())
                            {{ Auth::user()->firstname }} {{ Auth::user()->lastname }}
                        @elseif(isset($user))
                            {{ $user->firstname }} {{ $user->lastname }}
                        @else
                            Kent Fuller
                        @endif
                    </span>
                    <i class="fas fa-chevron-down ml-2"></i>
                    <div class="user-dropdown-menu">
                        <a href="{{ url('/account') }}" class="dropdown-item">
                            <i class="fas fa-user"></i>
                            <span>My Profile</span>
                        </a>
                        <a href="{{ url('/account/settings') }}" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{{ url('/logout') }}" class="dropdown-item" id="logoutLink">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-body">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            @if(session('info'))
                <div class="alert alert-info">
                    {{ session('info') }}
                </div>
            @endif

            @yield('content')
        </div>
    </div>

    <!-- Hidden form for logout -->
    <form id="logout-form" action="{{ url('/logout') }}" method="POST" style="display: none;">
        @csrf
    </form>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ asset('js/dashboard-menu.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded');

            // User profile dropdown toggle
            const userProfile = document.getElementById('userProfileDropdown');

            if (userProfile) {
                console.log('User profile dropdown found');

                userProfile.addEventListener('click', function(e) {
                    console.log('User profile clicked');
                    this.classList.toggle('active');
                    console.log('Dropdown active state:', this.classList.contains('active'));
                    e.stopPropagation();
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!userProfile.contains(e.target)) {
                        console.log('Clicked outside dropdown, closing it');
                        userProfile.classList.remove('active');
                    }
                });
            } else {
                console.error('User profile dropdown not found!');
            }

            // Logout link handler - using direct link instead of form submission
            const logoutLink = document.getElementById('logoutLink');
            if (logoutLink) {
                console.log('Logout link found');

                logoutLink.addEventListener('click', function(e) {
                    console.log('Logout link clicked');
                    // No need to prevent default or submit form - just let the link work normally
                });
            } else {
                console.error('Logout link not found!');
            }

            // If on mygroups route, ensure main nav is expanded
            if (document.body.classList.contains('mygroups-route')) {
                const mainMenu = document.querySelector('.main-menu');
                if (mainMenu) {
                    mainMenu.classList.add('expanded');
                }
            }
        });
    </script>
    @stack('scripts')
    @yield('scripts')
</body>
</html>
