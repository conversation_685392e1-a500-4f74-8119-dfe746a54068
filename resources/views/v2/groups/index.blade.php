@extends('layouts.dashboard-new')

@push('styles')
<style>
    /* Groups page styles - using more specific selectors to override dashboard styles */
    .content-body .container {
        margin: 0 auto;
        padding: 20px;
        background-color: transparent !important;
    }

    /* Reset Bootstrap styles that might interfere */
    .content-body .container .row {
        margin-left: 0;
        margin-right: 0;
    }

    .content-body .nav-tabs {
        display: flex;
        gap: 30px;
        margin-bottom: 30px;
        border-bottom: 1px solid #e0e0e0;
        background: transparent;
    }

    .content-body .nav-tab {
        padding: 12px 0;
        color: #666;
        text-decoration: none;
        font-weight: 500;
        position: relative;
    }

    .content-body .nav-tab.active {
        color: #6366f1;
    }

    .content-body .nav-tab.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #6366f1;
    }

    .content-body .filters {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
        align-items: center;
    }

    .content-body .filter-dropdown {
        position: relative;
    }

    .content-body .dropdown-btn {
        padding: 10px 15px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #666;
    }

    .content-body .dropdown-btn:hover {
        border-color: #bbb;
    }

    /* Override dashboard search container styles */
    .content-body .container .search-container {
        flex: 1;
        max-width: 400px;
        background: transparent;
        padding: 0;
        border-radius: 0;
    }

    /* Override dashboard search input styles */
    .content-body .container .search-input {
        width: 100%;
        padding: 10px 15px 10px 40px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2'%3E%3Ccircle cx='11' cy='11' r='8'/%3E%3Cpath d='m21 21-4.35-4.35'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: 12px center;
        height: auto;
    }

    .content-body .search-input:focus {
        outline: none;
        border-color: #6366f1;
    }

    .content-body .section-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 30px;
        color: #333;
    }

    .content-body .groups-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
    }

    .content-body .group-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .content-body .group-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .content-body .group-image {
        height: 200px;
        background-size: cover;
        background-position: center;
        position: relative;
        display: flex;
        align-items: flex-end;
        padding: 20px;
    }

    .content-body .group-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .content-body .group-content {
        padding: 20px;
    }

    .content-body .group-badge {
        display: inline-block;
        padding: 4px 8px;
        background-color: #e0e7ff;
        color: #6366f1;
        font-size: 12px;
        font-weight: 500;
        border-radius: 4px;
        text-transform: uppercase;
        margin-bottom: 12px;
    }

    .content-body .group-meta {
        margin-bottom: 12px;
    }

    .content-body .group-location {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
    }

    .content-body .group-description {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 20px;
    }

    .content-body .group-members {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
    }

    .content-body .member-avatar {
        width: 50px;
        height: 50px;
        border-radius: 5px;
        background-color: #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 500;
        color: #666;
        position: relative;
        /* visually indicate small size */
        box-shadow: 0 1px 2px rgba(0,0,0,0.04);
    }

    .content-body .member-avatar.james { background-color: #fef3c7; color: #92400e; }
    .content-body .member-avatar.person2 { background-color: #dbeafe; color: #1e40af; }
    .content-body .member-avatar.person3 { background-color: #fce7f3; color: #be185d; }
    .content-body .member-avatar.person4 { background-color: #d1fae5; color: #065f46; }

    .content-body .admin-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: #374151;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 3px;
        white-space: nowrap;
    }

    .content-body .group-actions {
        display: flex;
        gap: 12px;
    }

    /* Override Bootstrap button styles */
    .content-body .btn {
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .content-body .btn-primary {
        background-color: #6366f1;
        color: white !important;
        border: none;
    }

    .content-body .btn-primary:hover {
        background-color: #5856eb;
    }

    .content-body .btn-outline {
        background-color: transparent;
        color: #6366f1 !important;
        border: 1px solid #6366f1;
    }

    .content-body .btn-outline:hover {
        background-color: #6366f1;
        color: white !important;
    }

    .content-body .empty-state {
        text-align: center;
        padding: 40px;
        background: #f9fafb;
        border-radius: 12px;
    }

    .content-body .empty-state p {
        margin-bottom: 20px;
        color: #666;
    }

    @media (max-width: 768px) {
        .content-body .groups-grid {
            grid-template-columns: 1fr;
        }
        
        .content-body .filters {
            flex-direction: column;
            align-items: stretch;
        }
        
        .content-body .search-container {
            max-width: none;
        }
    }
</style>
@endpush

@section('content')
<div class="container">
    <nav class="nav-tabs">
        <a href="{{ route('mygroups') }}" class="nav-tab">Groups</a>
        <a href="{{ route('mygroups') }}" class="nav-tab active">All Groups</a>
    </nav>

    <div class="filters">
        <div class="filter-dropdown">
            <button class="dropdown-btn">
                From latest to oldest activity
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
            </button>
        </div>
        <div class="filter-dropdown">
            <button class="dropdown-btn">
                From A-Z
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
            </button>
        </div>
        <div class="search-container groups-search">
            <input type="text" class="search-input groups-search-input" placeholder="Search">
        </div>
    </div>

    <h1 class="section-title">All Groups</h1>

    <div class="groups-grid">
        @forelse($groups as $group)
            <div class="group-card">
                <div class="group-image" style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('{{ $group->group_banner_image ? asset('storage/' . $group->group_banner_image) : asset('images/group-placeholder.jpg') }}');">
                    <h3 class="group-title">{{ $group->name }}</h3>
                </div>
                <div class="group-content">
                    <div class="group-badge">
                        @if(isset($group->pivot) || isset($group->is_invite))
                            {{ isset($group->is_invite) ? 'Invited' : 'My Group' }}
                        @else
                            {{ $group->type == 'public' ? 'Public Group' : ($group->type == 'private' ? 'Private Group' : 'Secret Group') }}
                        @endif
                    </div>
                    <div class="group-meta">
                        <div class="group-location">
                            <strong>Location:</strong>
                            @if($group->city)
                                {{ $group->city }}{{ $group->state && $group->state != 'zzzz' ? ', ' . $group->state : '' }}{{ $group->country ? ', ' . $group->country : '' }}
                            @else
                                Not specified
                            @endif
                        </div>
                        <div class="group-description">
                            <strong>Description:</strong> 
                            {{ $group->description ?: 'This description needs to be filled in!' }}
                        </div>
                    </div>
                    <div class="group-members">
                        @php
                            $avatarColors = ['james', 'person2', 'person3', 'person4'];
                            $displayMembers = $group->members->take(4);
                            $remainingCount = $group->members->count() - 4;
                        @endphp
                        
                        @forelse($displayMembers as $index => $member)
                            <div class="member-avatar {{ $avatarColors[$index % count($avatarColors)] }}">
                                {{ strtoupper(substr($member->firstname, 0, 1) . substr($member->lastname, 0, 1)) }}
                                @if(isset($member->pivot) && $member->pivot->role == 'owner')
                                    <span class="admin-badge">Admin</span>
                                @endif
                            </div>
                        @empty
                            <div class="member-avatar">?</div>
                        @endforelse
                        
                        @if($remainingCount > 0)
                            <div class="member-avatar">+{{ $remainingCount }}</div>
                        @endif
                    </div>
                    <div class="group-actions">
                        @if(isset($group->pivot) || isset($group->is_invite))
                            <a href="{{ route('group.show', $group->id) }}" class="btn btn-primary">View Group</a>
                        @else
                            @if($group->type == 'public' || $group->type == 'private')
                                <a href="{{ route('group.join', $group->id) }}" class="btn btn-outline">Request to Join</a>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="empty-state">
                <p>No groups found. You can create a new group or join existing ones.</p>
                <a href="{{ route('group.create') }}" class="btn btn-primary">Create a Group</a>
                <a href="{{ route('group.index') }}" class="btn btn-outline">Find Groups</a>
            </div>
        @endforelse
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Search functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Use more specific selectors to avoid conflicts with dashboard
        const searchInput = document.querySelector('.content-body .groups-search-input');
        const groupCards = document.querySelectorAll('.content-body .group-card');
        
        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                
                groupCards.forEach(card => {
                    const title = card.querySelector('.group-title').textContent.toLowerCase();
                    const description = card.querySelector('.group-description').textContent.toLowerCase();
                    const location = card.querySelector('.group-location').textContent.toLowerCase();
                    
                    if (title.includes(searchTerm) || description.includes(searchTerm) || location.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // Dropdown functionality
        const dropdownBtns = document.querySelectorAll('.content-body .dropdown-btn');
        
        dropdownBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // This would typically open a dropdown menu
                console.log('Dropdown clicked:', this.textContent.trim());
            });
        });

        // Navigation tabs
        const navTabs = document.querySelectorAll('.content-body .nav-tab');
        
        navTabs.forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all tabs
                navTabs.forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
            });
        });
    });
</script>
@endpush 