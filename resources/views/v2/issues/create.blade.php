@extends('layouts.dashboard-new')

@inject('locationHelper', 'App\\Models\\LocationHelper')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-requests.css') }}">
<style>
    .content-body .container {
        margin: 0 20px;
        padding: 40px 0 0 0;
        max-width: 700px;
        background: transparent;
    }
    .content-body .v2-create-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 16px;
        color: #333;
    }
    .content-body .v2-create-desc {
        color: #444;
        font-size: 16px;
        margin-bottom: 24px;
    }
    .content-body .v2-create-note {
        background: #f5f3ff;
        border: 1px solid #e0e7ff;
        border-radius: 8px;
        padding: 18px 20px;
        margin-bottom: 32px;
        color: #5b4b8a;
        font-size: 15px;
    }
    .content-body .v2-create-form {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 32px 32px 24px 32px;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }
    .content-body .v2-form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    .content-body .v2-form-label {
        font-weight: 500;
        color: #333;
        font-size: 15px;
    }
    .content-body .v2-form-input, .content-body .v2-form-select, .content-body .v2-form-textarea {
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 15px;
        background: #fafbfc;
        color: #222;
        width: 100%;
        resize: none;
    }
    .content-body .v2-form-textarea {
        min-height: 90px;
    }
    .content-body .v2-form-select {
        appearance: none;
        background: #fafbfc url('data:image/svg+xml;utf8,<svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M6 8L10 12L14 8" stroke="%23999" stroke-width="2"/></svg>') no-repeat right 12px center/18px 18px;
    }
    .content-body .v2-form-hint {
        color: #888;
        font-size: 13px;
    }
    .content-body .v2-form-row {
        display: flex;
        gap: 20px;
    }
    .content-body .v2-form-row > .v2-form-group {
        flex: 1;
    }
    .content-body .v2-create-btn {
        background: #6c47ff;
        color: #fff;
        border: none;
        border-radius: 6px;
        padding: 12px 0;
        font-size: 16px;
        font-weight: 600;
        margin-top: 12px;
        cursor: pointer;
        transition: background 0.2s;
    }
    .content-body .v2-create-btn:hover {
        background: #5936d6;
    }
    .alert {
        margin-bottom: 24px;
    }
</style>
@endpush

@section('title', 'Make a Request')

@section('content')
<div class="container">
    @include('common.alerts')
    @if (Session::has('alerts.warning'))
        <div class="v2-warning-alert" style="background: #fffbe6; border: 1px solid #ffe58f; color: #ad8b00; border-radius: 8px; padding: 16px 20px; margin-bottom: 24px; display: flex; align-items: center; gap: 12px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 20px;"></i>
            <span>{{ Session::get('alerts.warning') }}</span>
        </div>
    @endif
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul style="margin:0; padding-left: 20px;">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="v2-create-title">Make a Request</div>
    <div class="v2-create-desc">
        Enter the details below to create a new need request. Keep in mind that a need could be fulfilled in ways other than financial, such as volunteering time, skills and services to recipients. If you do not have a specific deadline for the need, a good time period is 2 weeks to give group members time to respond.
    </div>
    <form class="v2-create-form" method="POST" action="{{ route('issue.confirm') }}">
        @csrf
        <input type="hidden" name="discussion_id" value="{{ $discussion_id ?? 0 }}">
        <input type="hidden" name="group_id" value="{{ $group->id }}">
        <div class="v2-form-row">
            <div class="v2-form-group">
                <label class="v2-form-label" for="title">Request Title</label>
                <input class="v2-form-input" type="text" id="title" name="title" placeholder="Request Title" value="{{ old('title') }}" required>
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="amount">Request Total</label>
                <input class="v2-form-input" type="number" step="0.01" min="0" id="amount" name="proposal[amount]" placeholder="$0.00" value="{{ old('proposal.amount') }}" required>
            </div>
        </div>
        <div class="v2-form-group">
            <label class="v2-form-label" for="description">Description</label>
            <textarea class="v2-form-textarea" id="description" name="description" placeholder="Enter a detailed description of the need request here. Summarize the reason for the need, your relationship to the recipient and what impact you think the need would make." required>{{ old('description') }}</textarea>
        </div>
        <div class="v2-form-row">
            <div class="v2-form-group">
                <label class="v2-form-label" for="deadline">Deadline</label>
                <select class="v2-form-select" id="deadline" name="proposal[extend_deadline]" required>
                    @foreach($deadline_choices as $key => $label)
                        <option value="{{ $key }}" @if(old('proposal.extend_deadline') == $key) selected @endif>{{ $label }}</option>
                    @endforeach
                </select>
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="category">Category</label>
                <select class="v2-form-select" id="category" name="issue_category_id" required>
                    @foreach(App\Models\IssueCategory::select_options('Select Category') as $key => $label)
                        <option value="{{ $key }}" @if(old('issue_category_id') == $key) selected @endif>{{ $label }}</option>
                    @endforeach
                </select>
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="type">Type of Request</label>
                <select class="v2-form-select" id="type" name="issue_type_id" required>
                    @foreach(App\Models\IssueType::select_options('Select Type') as $key => $label)
                        <option value="{{ $key }}" @if(old('issue_type_id') == $key) selected @endif>{{ $label }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="v2-form-row">
            <div class="v2-form-group">
                <label class="v2-form-label" for="recipient_firstname">Recipient First Name</label>
                <input class="v2-form-input" type="text" id="recipient_firstname" name="recipient[firstname]" placeholder="First Name" value="{{ old('recipient.firstname') }}" required>
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="recipient_lastname">Recipient Last Name</label>
                <input class="v2-form-input" type="text" id="recipient_lastname" name="recipient[lastname]" placeholder="Last Name" value="{{ old('recipient.lastname') }}" required>
            </div>
        </div>
        <button class="v2-create-btn" type="submit">Submit Request</button>
    </form>
</div>
@endsection 