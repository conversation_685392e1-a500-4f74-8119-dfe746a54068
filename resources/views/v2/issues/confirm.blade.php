@extends('layouts.dashboard-new')

@inject('locationHelper', 'App\\Models\\LocationHelper')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-requests.css') }}">
<style>
    .content-body .container {
        margin: 0 20px;
        padding: 40px 0 0 0;
        max-width: 700px;
        background: transparent;
    }
    .v2-confirm-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 16px;
        color: #333;
    }
    .v2-confirm-desc {
        color: #444;
        font-size: 16px;
        margin-bottom: 24px;
    }
    .v2-confirm-form {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 32px 32px 24px 32px;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }
    .v2-confirm-row {
        display: flex;
        gap: 20px;
        margin-bottom: 12px;
    }
    .v2-confirm-label {
        font-weight: 500;
        color: #333;
        min-width: 140px;
        flex-shrink: 0;
    }
    .v2-confirm-value {
        color: #5144A1;
        font-size: 16px;
        font-weight: 500;
        flex: 1;
    }
    .v2-confirm-btn-row {
        display: flex;
        gap: 16px;
        margin-top: 24px;
    }
    .v2-confirm-btn {
        background: #6c47ff;
        color: #fff;
        border: none;
        border-radius: 6px;
        padding: 12px 32px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s;
    }
    .v2-confirm-btn:hover {
        background: #5936d6;
    }
    .v2-confirm-btn-outline {
        background: #fff;
        color: #6c47ff;
        border: 2px solid #6c47ff;
    }
    .v2-confirm-btn-outline:hover {
        background: #f5f3ff;
    }
</style>
@endpush

@section('title', 'Confirm Request')

@section('content')
<div class="container">
    <div class="v2-confirm-title">Review Your Request</div>
    <div class="v2-confirm-desc">
        Please review the details below before submitting your request. If you need to make changes, click Edit. Otherwise, click Submit to finalize your request.
    </div>
    <form class="v2-confirm-form" method="POST" action="{{ route('issue.store') }}">
        @csrf
        <input type="hidden" name="discussion_id" value="{{ $input['discussion_id'] ?? 0 }}">
        <input type="hidden" name="group_id" value="{{ $group->id }}">
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Request Title:</span>
            <span class="v2-confirm-value">{{ $input['title'] }}</span>
            <input type="hidden" name="title" value="{{ $input['title'] }}">
        </div>
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Description:</span>
            <span class="v2-confirm-value">{{ $input['description'] }}</span>
            <input type="hidden" name="description" value="{{ $input['description'] }}">
        </div>
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Request Total:</span>
            <span class="v2-confirm-value">{{ $group->currency }}{{ $input['proposal']['amount'] }}</span>
            <input type="hidden" name="proposal[amount]" value="{{ $input['proposal']['amount'] }}">
        </div>
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Deadline:</span>
            <span class="v2-confirm-value">{{ $input['proposal']['extend_deadline'] }} days</span>
            <input type="hidden" name="proposal[extend_deadline]" value="{{ $input['proposal']['extend_deadline'] }}">
        </div>
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Category:</span>
            <span class="v2-confirm-value">{{ App\Models\IssueCategory::find($input['issue_category_id'])->description ?? '' }}</span>
            <input type="hidden" name="issue_category_id" value="{{ $input['issue_category_id'] }}">
        </div>
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Type of Request:</span>
            <span class="v2-confirm-value">{{ App\Models\IssueType::find($input['issue_type_id'])->description ?? '' }}</span>
            <input type="hidden" name="issue_type_id" value="{{ $input['issue_type_id'] }}">
        </div>
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Recipient First Name:</span>
            <span class="v2-confirm-value">{{ $input['recipient']['firstname'] ?? '' }}</span>
            <input type="hidden" name="recipient[firstname]" value="{{ $input['recipient']['firstname'] ?? '' }}">
        </div>
        <div class="v2-confirm-row">
            <span class="v2-confirm-label">Recipient Last Name:</span>
            <span class="v2-confirm-value">{{ $input['recipient']['lastname'] ?? '' }}</span>
            <input type="hidden" name="recipient[lastname]" value="{{ $input['recipient']['lastname'] ?? '' }}">
        </div>
        <div class="v2-confirm-btn-row">
            <a href="javascript:history.back()" class="v2-confirm-btn v2-confirm-btn-outline">Edit</a>
            <button type="submit" class="v2-confirm-btn">Submit Request</button>
        </div>
    </form>
</div>
@endsection 