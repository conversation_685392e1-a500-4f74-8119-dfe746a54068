<nav class="main-menu" aria-label="Sidebar">
    <div class="menu-header">
        <div class="welcome-text">WELCOME</div>
        <div class="menu-toggle">
            <i class="fas fa-bars" aria-hidden="true"></i>
        </div>
    </div>

    <div class="logo-container">
        <img src="{{ asset('images/logo-cc-blue.png') }}" alt="Common Change">
    </div>


    <div class="menu-section">
        <div class="menu-item {{ request()->routeIs('dashboard.*') ? 'active' : '' }}">
            <a href="/dashboard" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-th-large" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Activity</span>
            </a>
        </div>
    </div>



    <div class="menu-section">
        <div class="section-title">GROUPS</div>

        <div class="menu-item group-dropdown-trigger {{ request()->routeIs('group.*') ? 'active' : '' }}" style="cursor: pointer;">
            <div class="menu-icon">
                <i class="fas fa-users" aria-hidden="true"></i>
            </div>
            <span class="menu-text">{{ Auth::user()->get_current_group()->name }}</span>
            <i class="fas fa-chevron-down dropdown-icon" aria-hidden="true"></i>
            <div class="submenu-container" style="position: absolute; left: 100%; top: 0; width: var(--main-menu-collapsed-width);">
                <div class="group-dropdown" style="display: none; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); z-index: 9999; min-width: 200px; max-height: 300px; overflow: hidden; transform-origin: top; transform: scaleY(0); transition: transform 0.2s ease-out;">
                    @foreach(Auth::user()->groups()->orderBy('name')->get() as $group)
                        <a href="{{ route('group.set_current', ['id' => $group->id]) }}"
                           class="dropdown-item group-switch-link"
                           data-group-url="{{ route('group.set_current', ['id' => $group->id]) }}"
                           style="display: block; padding: 10px 16px; color: inherit; text-decoration: none; border-bottom: 1px solid #eee; {{ $group->id === Auth::user()->get_current_group()->id ? 'background: #f0f0f0; font-weight: bold;' : '' }}">
                            {{ $group->name }}
                        </a>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="menu-item {{ request()->routeIs('group.home') ? 'active' : '' }}">
            <a href="/group/{{ Auth::user()->get_current_group()->id }}" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-home" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Group Home</span>
            </a>
        </div>

        <div class="menu-item {{ request()->routeIs('finances.*') ? 'active' : '' }}">
            <div class="menu-icon">
                <i class="fas fa-hand-holding-heart" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Your Giving</span>
        </div>

        <div class="menu-item {{ request()->routeIs('issue.create') ? 'active' : '' }}">
            <a href="/group/{{ Auth::user()->get_current_group()->id }}/issue/create" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-clipboard-list" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Make a Request</span>
            </a>
        </div>

        <div class="menu-item">
            <div class="menu-icon">
                <i class="fas fa-question-circle" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Ask for Help</span>
        </div>
        <div class="menu-item {{ request()->is('group/create') ? 'active' : '' }}">
            <a href="/group/create" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-users-cog" aria-hidden="true"></i>
                </div>
                <span class="menu-text">Create New Group</span>
            </a>
        </div>
        <div class="menu-item {{ request()->is('mygroups') ? 'active' : '' }}">
            <a href="/mygroups" style="display: flex; align-items: center; text-decoration: none; color: inherit; width: 100%;">
                <div class="menu-icon">
                    <i class="fas fa-users-cog" aria-hidden="true"></i>
                </div>
                <span class="menu-text">All Groups</span>
            </a>
        </div>
    </div>

    <div class="menu-section">
        <div class="section-title">ACCOUNT</div>

        <div class="menu-item {{ request()->routeIs('account.profile') ? 'active' : '' }}">
            <div class="menu-icon">
                <i class="fas fa-user" aria-hidden="true"></i>
            </div>
            <span class="menu-text">My Account</span>
        </div>

        <div class="menu-item">
            <div class="menu-icon">
                <i class="fas fa-box" aria-hidden="true"></i>
            </div>
            <span class="menu-text">My Items</span>
        </div>

        <div class="menu-item">
            <div class="menu-icon">
                <i class="fas fa-share-alt" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Spread the Word</span>
        </div>
    </div>

    <div class="menu-section">
        <div class="section-title">RESOURCES</div>

        <div class="menu-item">
            <div class="menu-icon">
                <i class="fas fa-tools" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Tools & Guidance</span>
        </div>
    </div>

    @if(Auth::check() && Auth::user()->is_admin)
    <div class="menu-section">
        <div class="section-title">ADMIN</div>

        <div class="menu-item {{ request()->routeIs('admin.*') ? 'active' : '' }}">
            <div class="menu-icon">
                <i class="fas fa-user-shield" aria-hidden="true"></i>
            </div>
            <span class="menu-text">Admin</span>
        </div>
    </div>
    @endif
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const groupDropdown = document.querySelector('.group-dropdown');
    const dropdownIcon = document.querySelector('.dropdown-icon');
    const groupDropdownTrigger = document.querySelector('.group-dropdown-trigger');
    const mainMenu = document.querySelector('.main-menu');
    const submenuContainer = document.querySelector('.submenu-container');

    function updateDropdownPosition() {
        const isExpanded = mainMenu.classList.contains('expanded');
        if (submenuContainer) {
            // Update both width and left position based on menu state
            const width = isExpanded ?  $('.submenu-container').css('left', '245px') : $('.submenu-container').css('left', '70px') ;
            submenuContainer.style.width = width;
            submenuContainer.style.left = width;
        }
    }

    // Initial position update
    updateDropdownPosition();

    // Listen for menu toggle clicks
    const menuToggle = document.querySelector('.menu-toggle');
    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            // Wait for the menu class to be updated
            requestAnimationFrame(updateDropdownPosition);
        });
    }

    // Make the entire row clickable for dropdown toggle
    if (groupDropdownTrigger) {
        groupDropdownTrigger.addEventListener('click', function(e) {
            e.stopPropagation();
            if (groupDropdown.style.display === 'none' || groupDropdown.style.display === '') {
                groupDropdown.style.display = 'block';
                // Trigger reflow
                groupDropdown.offsetHeight;
                groupDropdown.style.transform = 'scaleY(1)';
                updateDropdownPosition();
            } else {
                groupDropdown.style.transform = 'scaleY(0)';
                setTimeout(() => {
                    groupDropdown.style.display = 'none';
                }, 200);
            }
        });
    }

    // Update position when menu expands/collapses
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'class') {
                requestAnimationFrame(updateDropdownPosition);
            }
        });
    });

    observer.observe(mainMenu, { attributes: true });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            mainMenu.classList.remove('expanded');
        }
        updateDropdownPosition();
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!menuItem.contains(e.target)) {
            groupDropdown.style.transform = 'scaleY(0)';
            setTimeout(() => {
                groupDropdown.style.display = 'none';
            }, 200);
        }
    });

    // Intercept group switch links to always redirect to dashboard after switching
    document.querySelectorAll('.group-switch-link').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const url = this.getAttribute('data-group-url');
            window.location.href = url + '?redirect_dashboard=1';
        });
    });
});
</script>
