<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Group;
use App\Models\Discussion;
use App\Models\Issue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;

class DashboardController extends Controller
{
    /**
     * Constructor to apply middleware
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user's dashboard.
     *
     * @param  int  $userId
     * @return \Illuminate\View\View
     */
    public function show($userId)
    {
        // Debug information
        \Log::info('Dashboard show method called with user ID: ' . $userId);
        \Log::info('Current authenticated user ID: ' . (Auth::check() ? Auth::id() : 'Not authenticated'));
        \Log::info('Session data in dashboard', [
            'session_id' => session()->getId(),
            'auth_checked' => session('auth_checked'),
            'session_user_id' => session('user_id'),
            'all_session_data' => session()->all()
        ]);

        // Ensure the user is authenticated (this is a backup check, as we already have the auth middleware)
        if (!Auth::check()) {
            \Log::error('Unauthenticated user attempting to access dashboard');

            // The CheckAuthStatus middleware should have already tried to re-authenticate
            // If we're still not authenticated, redirect to login
            return redirect()->route('auth.login');
        }

        // Check if the user exists
        $user = User::find($userId);

        if (!$user) {
            \Log::error('User not found with ID: ' . $userId);
            return redirect()->route('auth.login')->withErrors(['error' => 'User not found']);
        }

        // Only allow users to view their own dashboard unless they're an admin
        if (Auth::id() != $userId && !Auth::user()->is_admin) {
            \Log::warning('Unauthorized dashboard access attempt', [
                'authenticated_user' => Auth::id(),
                'requested_user' => $userId
            ]);
            return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
        }

        // Get recent discussions
        try {
            $recentDiscussions = Discussion::with(['group', 'user'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            $recentDiscussions = collect();
        }

        // Get recent issues (requests)
        try {
            $recentIssues = Issue::with(['group', 'user'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            $recentIssues = collect();
        }

        // Get user's groups
        try {
            $userGroups = Group::whereHas('members', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })->get();
        } catch (\Exception $e) {
            $userGroups = collect();
        }

        return view('dashboard.new', [
            'user' => $user,
            'recentDiscussions' => $recentDiscussions,
            'recentIssues' => $recentIssues,
            'userGroups' => $userGroups
        ]);
    }

    public function show_v2($userId)
    {
        // Same logic as show()
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }
        $user = User::find($userId);
        if (!$user) {
            return redirect()->route('auth.login')->withErrors(['error' => 'User not found']);
        }
        if (Auth::id() != $userId && !Auth::user()->is_admin) {
            return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
        }
        try {
            $recentDiscussions = Discussion::with(['group', 'user'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            $recentDiscussions = collect();
        }
        try {
            $recentIssues = Issue::with(['group', 'user'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            $recentIssues = collect();
        }
        try {
            $userGroups = Group::whereHas('members', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })->get();
        } catch (\Exception $e) {
            $userGroups = collect();
        }
        return view('v2.dashboard', [
            'user' => $user,
            'recentDiscussions' => $recentDiscussions,
            'recentIssues' => $recentIssues,
            'userGroups' => $userGroups
        ]);
    }
}
