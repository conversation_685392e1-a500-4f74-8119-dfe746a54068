<?php

namespace App\Http\Controllers;

use App\Models\Group;
use App\Models\OnesignalApiInterface;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class GroupController extends Controller
{
    private function add_search_sort($q, $input)
    {
        $search = $input['search'] ?? '';
        if (! empty($search)) {
            $q = $q->where('name', 'like', '%'.$search.'%');
        }

        switch ($input['sort'] ?? '') {
            case 'revalpha':
                $q = $q->orderBy('name', 'desc');
                break;
            case 'state':
                $q = $q->orderBy('state')->orderBy('name');
                break;
            case 'country':
                $q = $q->orderBy('country')->orderBy('name');
                break;
            default:
                $q = $q->orderBy('name');
                break;
        }

        return $q;
    }

    private function duple_groups($groups)
    {
        // This page displays groups in duples, so let's get that sorted here:
        $duple_groups = [];
        $even = true;
        $key = 0;
        foreach ($groups as $group) {
            $duple_key = floor($key / 2);
            if ($even) {
                $duple_groups[$duple_key] = [];
                $class = 'padd-four';
            } else {
                $class = 'padd-five';
            }
            $group['duple_class'] = $class;
            $duple_groups[$duple_key][] = $group;
            $even = ! $even;
            $key++;
        }

        return $duple_groups;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::User();
        $query = Group::notSuspended()->has('members', '>', 1);
        if (! $user->is_admin) {
            $query = $query->where(function ($q) use ($user) {
                return $q->notSecret()
                    ->orWhere->hasMember($user)
                    ->orWhere->hasInvited($user);
            });
        }

        $query = $this->add_search_sort($query, $request->all());
        $groups = $query->paginate(10)->appends($request->except('page'));

        return view('groups.index')->withGroups($this->duple_groups($groups))
            ->withPGroups($groups)
            ->withInput($request->all());
    }

    public function wizard_index(Request $request)
    {
        $user = Auth::User();

        // First, get all groups the user is invited to:
        $invited_groups = Group::notSuspended()
            ->hasInvited($user)
            ->orderBy('name')
            ->get();

        // Next, get paginated, viewable groups, excluding invited groups:
        $query = Group::notSuspended()->has('members', '>', 1)->hasNotInvited($user);
        if (! $user->is_admin) {
            $query = $query->where(function ($q) use ($user) {
                return $q->notSecret()
                    ->orWhere->hasMember($user);
            });
        }
        $query = $this->add_search_sort($query, $request->all());
        $groups = $query->paginate(10)->appends($request->except('page'));

        return view('wizard.group')->withGroups($this->duple_groups($groups))
            ->withPGroups($groups)
            ->withInvitedGroups($invited_groups)
            ->withInput($request->all());
    }

    public function admin_index(Request $request): \Illuminate\View\View
    {
        $query = Group::withTrashed();
        $query = $this->add_search_sort($query, $request->all());
        $paged_groups = $query->paginate(50)->appends($request->except('page'));

        return view('groups.admin_index')->withGroups($paged_groups)->withInput($request->all());
    }

    public function my_groups(): \Illuminate\View\View
    {
        $user = Auth::User();
        $user->load('group_invites.group');

        // 'ORDER BY groups.id=1' puts the CommonChange group at the end of the list
        $groups = $user->groups()->orderByRaw('groups.id=1')->orderBy('name')->get();
        foreach ($user->group_invites as $invite) {
            $groups->prepend($invite->group);  // invited groups will appear at beginning of list
        }

        $groups->each(function ($group) {
            $group->load('members', 'discussions', 'issues');
        });

        return view('v2.groups.index')->withGroups($groups);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): \Illuminate\View\View
    {
        return view('v2.groups.create')->withGroup(new Group)->withIsNew(true);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $onCompletion = null): RedirectResponse
    {
        $user = Auth::User();
        $group = new Group;
        $group->fill($request->all());
        $group->url = $request->input('shortname');
        if (empty($group->shortname)) {
            $group->shortname = null;
        }
        if (! $group->isValid()) {
            return back()->withInput()->withErrors($group->errors);
        }

        $group->save();
        $group->members()->attach($user, ['role' => 'owner']);
        $user->default_group_id = $group->id;
        $user->save();

        $destination = (isset($onCompletion)) ? $onCompletion() : redirect()->route('mygroups');

        $mail_data = [
            'group' => $group,
            'user' => $user,
        ];
        Mail::send(['emails.new_group', 'emails.new_group-text'], $mail_data, function ($message) use ($group) {
            $message->to('<EMAIL>');
            $message->subject('New group: '.$group->name);
        });

        return $destination->withAlerts(['success' => 'Group successfully created!']);
    }

    public function wizard_create(): \Illuminate\View\View
    {
        return view('wizard.group_create')->withGroup(new Group);
    }

    public function wizard_store(Request $request)
    {
        return $this->store($request, function () {
            $user = Auth::User();
            $user->finishedWizardSection('group');
            $user->save();

            return redirect()->route($user->currentWizardRoute());
        });
    }

    public function wizard_join(Request $request, $id)
    {
        return $this->join($request, $id, function () {
            $user = Auth::User();
            $user->finishedWizardSection('group');
            $user->save();

            return redirect()->route('wizard');
        });
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, int $id)
    {
        $group = Group::find($id);
        if (! isset($group)) {
            abort(404);
        }
        if (! $group->canView(Auth::User())) {
            return redirect()->route('group.index')->withAlerts(['warning' => 'Unable to view group.']);
        }

        // Use the new v2 group details view
        return view('v2.groups.show')->withGroup($group)
            ->withPublic(false)
            ->withWizard($request->input('wizard', false))
            ->with('is_nonmember', ! $group->members->contains(Auth::User()));
    }

    public function group_home(Request $request)
    {
        $user = Auth::User();
        $group = $user->default_group()->first();
        if (! (isset($group) && $group->isMember($user))) {
            $group = $user->groups()->first();
            if (! isset($group)) {
                $group = Group::find(1);
                if (! (isset($group) && $group->isMember($user))) {
                    return redirect()->route('group.index')->withAlerts(['info' => "You're currently not a member of any group."]);
                }
            }
        }

        $query = $group->discussions_and_issues()->with('issue');

        // Did they use the filter/sort form?
        $input = $request->all();
        if (! empty($input['from_date'])) {
            $query = $query->where('discussions.created_at', '>=', Carbon::parse($input['from_date']));
        }
        if (! empty($input['to_date'])) {
            $query = $query->where('discussions.created_at', '<=', Carbon::parse($input['to_date']));
        }
        if (! empty($input['search'])) {
            $query = $query->where('discussions.title', 'like', '%'.$input['search'].'%');
        }

        switch ($request->input('sort', 'newest')) {
            case 'newest':
                $query = $query->orderBy('updated_at', 'desc');
                break;
            case 'oldest':
                $query = $query->orderBy('updated_at', 'asc');
                break;
            case 'alpha':
                $query = $query->orderBy('title', 'asc');
                break;
            case 'revalpha':
                $query = $query->orderBy('title', 'desc');
                break;
        }

        $paged_discussions = $query->paginate(15)->appends($request->except('page'));

        return view('home')->withGroup($group)->withDiscussions($paged_discussions)->withInput($request->all());
    }

    private function public_show($group)
    {
        if (! (isset($group) && $group->isPublicViewable())) {
            abort(404);
        }

        if (Auth::guest() || Auth::User()->suspended || ! Auth::User()->email_valid) {
            return view('groups.show')->withGroup($group)->withPublic(true);
        } else {
            return redirect()->route('group.show', ['group' => $group->id]);
        }
    }

    public function public_show_by_id($id)
    {
        return $this->public_show(Group::find($id));
    }

    public function public_show_by_shortname($shortname)
    {
        return $this->public_show(Group::where('shortname', $shortname)->first());
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id)
    {
        $group = Group::find($id);
        if (! isset($group)) {
            abort(404);
        }
        if (! $group->canEdit(Auth::User())) {
            return redirect()->route('group.show', $group->id)->withAlerts(['warning' => 'Unable to edit group.']);
        }

        return view('v2.groups.edit')->withGroup($group);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id)
    {
        $group = Group::findOrFail($id);
        if (! $group->canEdit(Auth::User())) {
            return back()->withAlerts(['warning' => "You don't have sufficient privileges to edit this group."]);
        }

        $data = $request->all();
        // Keep the existing shortname if not changed
        if (empty($data['shortname'])) {
            $data['shortname'] = $group->shortname;
        }
        // Update URL based on shortname
        $data['url'] = $data['shortname'];

        $group->fill($data);
        if (! $group->isValid()) {
            return back()->withInput()->withErrors($group->errors)
                ->withAlerts(['danger' => 'Unable to update group. Please see errors below.']);
        }

        $group->save();

        return redirect()->route('group.show', $group->id)
            ->withAlerts(['success' => 'Group updated successfully.']);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): Response
    {
        //
    }

    // The user tries to join a group.  Different things happen depending on whether the group is public / private / secret.
    public function join(Request $request, $id, $onSuccess = null)
    {
        $validation = Validator::make($request->all(), ['accept_covenant' => 'required']);
        if (! $validation->passes()) {
            return back()->withErrors($validation->messages())->withAlerts(['danger' => 'You must accept the terms of the Group Covenant to join']);
        }

        $group = Group::with('join_requests', 'members')->findOrFail($id);
        if ($group->suspended) {
            return redirect()->route('home')->withAlerts(['danger' => 'Unable to join suspended group.']);
        }

        $user = Auth::User();

        if ($group->members->contains($user)) {
            return back()->withAlerts(['warning' => "You're already a member of this group."]);
        }

        // -------- FOR PUBLIC GROUPS:
        if ($group->canJoin($user)) {
            $group->addMember($user);
            if (isset($onSuccess)) {
                $destination = $onSuccess();
            } else {
                $destination = back();
            }

            return $destination->withAlerts(['info' => "You've joined the group!"]);

            // -------- FOR PRIVATE GROUPS:
        } elseif ($group->type == 'private') {
            if ($group->join_requests->contains($user)) {
                return back()->withAlerts(['warning' => "You've already requested to join this group."]);
            }
            $group->join_requests()->attach($user);
            if (isset($onSuccess)) {
                $onSuccess();
            }

            Mail::send(['emails.group_join_request', 'emails.group_join_request-text'],
                ['addressee' => $group->owner(), 'user' => $user, 'group' => $group], function ($message) use ($group) {
                    $owner = $group->owner();
                    $message->to($owner->email, $owner->firstname.' '.$owner->lastname);
                    $message->subject('Commonchange: A user requested to join '.$group->name);
                });

            Mail::send(['emails.group_join_request_notification', 'emails.group_join_request_notification-text'],
                ['user' => $user, 'group' => $group], function ($message) use ($user, $group) {
                    $message->to('<EMAIL>', 'CommonChange Invites');
                    $message->subject('Group Join Request: '.$user->email.', '.$group->name);
                });

            return back()->withAlerts(['info' => 'Your request to join has been sent!']);

            // -------- FOR SECRET GROUPS:
        } elseif ($group->type == 'secret') {
            abort(404);
        }

        // we shouldn't reach this...
        return back();
    }

    public function leave($group_id)
    {
        $group = Group::with('members')->findOrFail($group_id);
        $member = $group->members->find(Auth::User());
        if (isset($member) && $member->pivot->role == 'owner') {
            return back()->withAlerts(['danger' => 'The group owner cannot leave the group.']);
        }

        $group->removeMember($member);

        return redirect('mygroups')->withAlerts(['success' => "You've left ".$group->name]);
    }

    // Show the admin page for the given group:
    public function admin($id)
    {
        $group = Group::with('members', 'join_requests')->findOrFail($id);
        if (! $group->canAdmin(Auth::User())) {
            return redirect('mygroups')->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        return view('groups.admin')->withGroup($group);
    }

    // Make give a group member the admin role
    public function toggle_admin($group_id, $user_id)
    {
        $group = Group::with('members')->findOrFail($group_id);
        if (! $group->canAdmin(Auth::User())) {
            return back()->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        $member = $group->members->find($user_id);
        if (isset($member) && $member->pivot->role != 'owner') {
            $new_role = ($member->pivot->role == 'member') ? 'admin' : 'member';
            $group->members()->updateExistingPivot($user_id, ['role' => $new_role]);

            return redirect()->route('group.admin', ['id' => $group->id])->withAlerts(['success' => "User role change to $new_role."]);
        } else {
            return redirect()->route('group.admin', ['id' => $group->id])->withAlerts(['danger' => 'Invalid user']);
        }
    }

    // Toggle a member's is_absent value
    public function toggle_absent($group_id, $user_id)
    {
        $group = Group::with('members')->findOrFail($group_id);
        if (! $group->canAdmin(Auth::User())) {
            return back()->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        $member = $group->members->find($user_id);
        if (isset($member)) {
            $group->members()->updateExistingPivot($user_id, ['is_absent' => (! $member->pivot->is_absent)]);

            return redirect()->route('group.admin', ['id' => $group->id])->withAlerts(['success' => "User's active status changed."]);
        } else {
            return redirect()->route('group.admin', ['id' => $group->id])->withAlerts(['danger' => 'Invalid user']);
        }
    }

    // ADMIN: add a user
    public function add_member($group_id, $user_id)
    {
        $group = Group::with('members')->findOrFail($group_id);
        if (! $group->canAdmin(Auth::User())) {
            return back()->withAlerts(['danger' => 'Insufficient privileges.']);
        }
    }

    public function remove_member($group_id, $user_id)
    {
        $group = Group::with('members')->findOrFail($group_id);
        if (! $group->canAdmin(Auth::User())) {
            return back()->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        $member = $group->members->find($user_id);
        if (isset($member) && $member->pivot->role != 'owner') {
            $group->removeMember($member);

            return redirect()->route('group.admin', ['id' => $group->id])->withAlerts(['success' => 'User removed.']);
        } else {
            return redirect()->route('group.admin', ['id' => $group->id])->withAlerts(['danger' => 'User cannot be removed.']);
        }
    }

    // ------------- DEALING WITH JOIN REQUESTS: ------------------//
    public function join_requests($group_id)
    {
        $group = Group::with('join_requests')->findOrFail($group_id);
        if (! $group->canAdmin(Auth::User())) {
            return redirect('mygroups')->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        return view('groups.join_requests')->withGroup($group);
    }

    public function accept_request($group_id, $user_id)
    {
        $group = Group::with('join_requests')->findOrFail($group_id);
        if (! $group->canAdmin(Auth::User())) {
            return redirect('mygroups')->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        $user = User::findOrFail($user_id);
        if (! $group->join_requests->contains($user)) {
            return redirect('mygroups')->withAlerts(['danger' => 'Invalid user.']);
        }

        $group->addMember($user);
        $group->join_requests()->detach($user);

        return back()->withAlerts(['success' => 'This user has been added to the group!']);
    }

    public function reject_request($group_id, $user_id)
    {
        $group = Group::with('join_requests')->findOrFail($group_id);
        if (! $group->canAdmin(Auth::User())) {
            return redirect('mygroups')->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        $user = User::findOrFail($user_id);
        if (! $group->join_requests->contains($user)) {
            return redirect('mygroups')->withAlerts(['danger' => 'Invalid user.']);
        }

        $group->join_requests()->detach($user);

        return back()->withAlerts(['info' => 'Join Request denied.']);
    }

    // ------------ SITE ADMIN SECTION: ------------//
    public function site_admin($group_id): \Illuminate\View\View
    {
        $group = Group::withTrashed()->findOrFail($group_id);

        return view('groups.site_admin')->withGroup($group);
    }

    public function admin_update(Request $request, $group_id)
    {
        $rules = [
            'current_funds' => 'required|numeric',
            'shared_funds' => 'required|numeric',
        ];
        $validation = Validator::make($request->all(), $rules);
        if ($validation->fails()) {
            return back()->withErrors($validation->messages());
        }

        $group = Group::withTrashed()->findOrFail($group_id);

        $old_current = $group->current_funds;
        $new_current = $request->input('current_funds');
        $group->current_funds = $new_current;
        $group->shared_funds = $request->input('shared_funds');
        $group->suspended = $request->has('suspended');
        $group->save();

        // Send mobile notification if funds have been added
        if ($new_current > $old_current) {
            $onesignal_ids = $group->getOneSignalIDs();
            if (count($onesignal_ids)) {
                $subject = 'Funds have been added for '.$group->name;
                $message = 'Current amount is '.$group->currency.$new_current.'. Know someone who might benefit from a gift? Post a request!';
                $url = URL::route('group.create_issue', ['id' => $group->id]);
                $mobile = new OnesignalApiInterface;
                $mobile->postToUsers($onesignal_ids, $subject, $message, $url);
            }
        }

        return back()->withAlerts(['success' => 'Changes saved.']);

    }

    public function toggleSuspended($group_id)
    {
        $admin = Auth::User();
        if (! $admin->is_admin) {
            abort(404);
        }

        $group = Group::find($group_id);

        if (! isset($group)) {
            return redirect()->route('home')->withAlerts(['danger' => 'Group not found']);
        }

        $group->suspended = ! $group->suspended;
        $group->save();
        $message = ($group->suspended) ? 'Group suspended.' : 'Group reactivated.';

        return back()->withAlerts(['info' => $message]);
    }

    public function update_member(Request $request, $group_id, $user_id)
    {
        $group = Group::with('members')->findOrFail($group_id);
        $me = Auth::User();
        if (! $group->canUpdateEmailPref($me) || $me->id != $user_id) {
            return back()->withAlerts(['danger' => 'Insufficient privileges.']);
        }

        $input = $request->only('email_notification');
        $errors = Group::validateUserPivot($input);
        // return $input;

        if (isset($errors)) {
            return back()->withAlerts(['danger' => 'Invalid input data.']);
        }

        $member = $group->members->find($user_id);
        if (isset($member)) {
            $group->members()->updateExistingPivot($user_id, $input);

            return back()->withAlerts(['success' => 'Email pref updated for '.$group->name]);
        } else {
            return redirect()->route('mygroups')->withAlerts(['danger' => 'Invalid user.']);
        }
    }

    public function updateBanner(Request $request, $id)
    {
        $group = Group::findOrFail($id);
        
        if (!$group->canEdit(Auth::user())) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'banner_image' => 'required|image|mimes:jpeg,png,jpg|max:5120' // 5MB max
        ]);

        if ($request->hasFile('banner_image')) {
            // Delete old banner if exists
            if ($group->group_banner_image) {
                Storage::disk('public')->delete($group->group_banner_image);
            }

            // Store new banner
            $path = $request->file('banner_image')->store('group-banners', 'public');
            \Log::info('Banner uploaded', [
                'group_id' => $id,
                'path' => $path,
                'old_banner' => $group->group_banner_image
            ]);
            
            $group->group_banner_image = $path;
            $group->save();

            \Log::info('Group updated', [
                'group_id' => $id,
                'banner_image' => $group->group_banner_image
            ]);

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false, 'message' => 'No image uploaded'], 400);
    }

    public function set_current($id)
    {
        $group = Group::findOrFail($id);
        $user = Auth::user();

        if (!$group->isMember($user)) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not a member of this group.'
                ]);
            }
            return back()->withAlerts(['danger' => 'You are not a member of this group.']);
        }

        User::set_current_group($group);
        
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Group switched successfully'
            ]);
        }
        // If redirect_dashboard=1 is present, redirect to dashboard
        if (request()->has('redirect_dashboard')) {
            return redirect()->route('dashboard.new', ['user_id' => Auth::id()]);
        }
        return redirect()->back();
    }
}
