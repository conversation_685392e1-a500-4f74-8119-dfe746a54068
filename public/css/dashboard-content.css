/*
 * Dashboard Content CSS
 * Styles for the dashboard content area
 */

:root {
    /* Primary colors */
    --primary-green: #9ACB48;
    --primary-purple: #5144A1;
    --primary-grey: #CECECE;
    --primary-navy: #4D5E80;
    --primary-black: #1A1A1A;

    /* Light colors */
    --light-cream: #F9F9F0;
    --light-lavender: #F0F0F9;
    --light-blue: #E9EFF2;
    --light-gray: #F7F8FA;
    --light-white: #FFFFFF;

    /* Font */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;

    /* Menu dimensions */
    --main-menu-width: 250px;
    --main-menu-collapsed-width: 70px;
    --submenu-width: 220px;
    --submenu-collapsed-width: 0px;
    --header-height: 60px;
}

/* Content area styles */
.content-wrapper {
    flex: 1;
    /* Since main menu is collapsed by default and submenu is open */
    margin-left: calc(var(--main-menu-collapsed-width) + var(--submenu-width));
    transition: margin-left 0.3s ease;
    min-height: 100vh;
    background-color: var(--light-gray);
}

.main-menu.expanded ~ .content-wrapper {
    margin-left: calc(var(--main-menu-width) + var(--submenu-width));
}

/* Hide these styles as we're not using collapsed submenu for now */
/*
.submenu-container.collapsed ~ .content-wrapper {
    margin-left: var(--main-menu-width);
}

.main-menu.expanded + .submenu-container.collapsed ~ .content-wrapper {
    margin-left: var(--main-menu-width);
}

.main-menu.collapsed + .submenu-container.collapsed ~ .content-wrapper {
    margin-left: var(--main-menu-collapsed-width);
}
*/

.content-header {
    height: var(--header-height);
    background-color: var(--light-white);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.header-left {
    display: flex;
    align-items: center;
}

.search-container {
    position: relative;
    width: 300px;
}

.search-input {
    width: 100%;
    height: 40px;
    border-radius: 20px;
    border: 1px solid var(--primary-grey);
    padding: 0 20px 0 40px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    border-color: var(--primary-purple);
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-navy);
    font-size: 16px;
}

.header-right {
    display: flex;
    align-items: center;
}

.notification-icon {
    position: relative;
    margin-right: 20px;
    cursor: pointer;
}

.notification-icon i {
    font-size: 20px;
    color: var(--primary-navy);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    background-color: var(--primary-green);
    color: var(--light-white);
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    color: var(--primary-navy);
    font-size: 14px;
    font-weight: 500;
}

.content-body {
    padding: 30px;
}

.page-title {
    margin-bottom: 30px;
    color: var(--primary-navy);
    font-size: 24px;
    font-weight: 600;
}

.activity-card {
    background-color: var(--light-white);
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    color: var(--primary-navy);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 4px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    text-decoration: none;
}

.btn-primary {
    color: var(--light-white);
    background-color: var(--primary-purple);
    border-color: var(--primary-purple);
}

.btn-primary:hover {
    background-color: #4535a0;
    border-color: #4535a0;
}

.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.card-body {
    padding: 20px;
}

.activity-item {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.activity-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.activity-content {
    flex: 1;
}

.activity-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
}

.activity-title {
    color: var(--primary-navy);
    font-size: 16px;
    font-weight: 500;
}

.activity-time {
    color: var(--primary-grey);
    font-size: 12px;
}

.activity-group {
    color: var(--primary-navy);
    font-size: 14px;
    margin-bottom: 5px;
}

.activity-description {
    color: var(--primary-navy);
    font-size: 14px;
    line-height: 1.5;
}

.activity-actions {
    display: flex;
    margin-top: 10px;
}

.activity-action {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    text-decoration: none;
    transition: color 0.2s ease;
}

.activity-action:hover {
    color: var(--primary-purple);
}

.activity-action i {
    font-size: 16px;
    color: var(--primary-navy);
    margin-right: 5px;
    transition: color 0.2s ease;
}

.activity-action:hover i {
    color: var(--primary-purple);
}

.activity-action span {
    color: var(--primary-navy);
    font-size: 14px;
    transition: color 0.2s ease;
}

.activity-action:hover span {
    color: var(--primary-purple);
}

.comment-form {
    margin-top: 20px;
}

.comment-box {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 20px;
    border: 1px solid var(--primary-grey);
    background-color: var(--light-white);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.comment-box:focus-within {
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
}

.comment-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 5px 10px;
    font-size: 14px;
    background-color: transparent;
}

.comment-button {
    background-color: var(--primary-purple);
    color: var(--light-white);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.comment-button:hover {
    background-color: #4535a0;
}

.empty-state {
    padding: 30px;
    text-align: center;
    color: var(--primary-navy);
}

.empty-state p {
    margin-bottom: 20px;
    font-size: 16px;
    opacity: 0.7;
}

/* Responsive styles */
@media (max-width: 992px) {
    /* Content wrapper is already positioned correctly for collapsed main menu */

    .main-menu.expanded ~ .content-wrapper {
        margin-left: calc(var(--main-menu-width) + var(--submenu-width));
    }
}

@media (max-width: 768px) {
    .content-wrapper {
        margin-left: 0;
    }

    .search-container {
        width: 200px;
    }

    .content-body {
        padding: 20px;
    }
}

/* Page Header Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--light-gray);
}

.page-header h1 {
    margin: 0;
    color: var(--primary-navy);
    font-size: 2rem;
    font-weight: 600;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* Filter Section Styles */
.filter-section {
    background: var(--light-white);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-form {
    margin: 0;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 500;
    color: var(--primary-navy);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.filter-group .form-control {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-group .form-control:focus {
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
    outline: none;
}

.search-input-group {
    display: flex;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.search-input-group .form-control {
    border: none;
    border-radius: 0;
    flex: 1;
}

.search-input-group .form-control:focus {
    box-shadow: none;
}

.search-input-group .btn {
    border: none;
    border-radius: 0;
    background: var(--primary-purple);
    color: white;
    padding: 0.75rem 1rem;
    transition: background-color 0.2s ease;
}

.search-input-group .btn:hover {
    background: #4a3d91;
}

/* Pagination Wrapper */
.pagination-wrapper {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
}

/* Activity Card Styles */
.activity-card {
    background: var(--light-white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.activity-card .card-body {
    padding: 0;
}

/* Activity Item Styles */
.activity-item {
    display: flex;
    padding: 1.5rem;
    border-bottom: 1px solid var(--light-gray);
    transition: background-color 0.2s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: var(--light-lavender);
}

.activity-avatar {
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-avatar img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-navy);
    font-size: 1.25rem;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.activity-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.activity-title a {
    color: var(--primary-navy);
    text-decoration: none;
    transition: color 0.2s ease;
}

.activity-title a:hover {
    color: var(--primary-purple);
}

.activity-time {
    color: #6b7280;
    font-size: 0.875rem;
    white-space: nowrap;
    margin-left: 1rem;
}

.activity-group {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0 0 0.75rem 0;
}

.activity-statuses {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-info {
    background: var(--light-blue);
    color: var(--primary-navy);
}

.badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.badge-success {
    background: #d1fae5;
    color: #065f46;
}

.badge-primary {
    background: var(--light-lavender);
    color: var(--primary-purple);
}

.activity-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.activity-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-navy);
    text-decoration: none;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.activity-action:hover {
    background: var(--light-lavender);
    color: var(--primary-purple);
    text-decoration: none;
}

.activity-action i {
    font-size: 1rem;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.empty-icon {
    font-size: 4rem;
    color: var(--primary-grey);
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: var(--primary-navy);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.empty-state p {
    margin-bottom: 2rem;
    font-size: 1rem;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-purple);
    color: white;
}

.btn-primary:hover {
    background: #4a3d91;
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: var(--light-gray);
    color: var(--primary-navy);
}

.btn-secondary:hover {
    background: #e5e7eb;
    color: var(--primary-navy);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .activity-item {
        flex-direction: column;
        gap: 1rem;
    }
    
    .activity-avatar {
        margin-right: 0;
        align-self: flex-start;
    }
    
    .activity-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .activity-time {
        margin-left: 0;
    }
    
    .activity-actions {
        justify-content: flex-start;
    }
}
