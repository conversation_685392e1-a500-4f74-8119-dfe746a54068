/*
 * Dashboard Menu CSS
 * Styles for the collapsible main menu and submenu
 */

:root {
    /* Primary colors */
    --primary-green: #9ACB48;
    --primary-purple: #5144A1;
    --primary-grey: #CECECE;
    --primary-navy: #4D5E80;
    --primary-black: #1A1A1A;

    /* Light colors */
    --light-cream: #F9F9F0;
    --light-lavender: #F0F0F9;
    --light-blue: #E9EFF2;
    --light-gray: #F7F8FA;
    --light-white: #FFFFFF;

    /* Font */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;

    /* Menu dimensions */
    --main-menu-width: 250px;
    --main-menu-collapsed-width: 70px;
    --submenu-width: 220px;
    --submenu-collapsed-width: 0px;
    --header-height: 60px;
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

body {
    background-color: var(--light-gray);
    min-height: 100vh;
    display: flex;
    overflow-x: hidden;
}

/* Main menu styles */
.main-menu {
    /* Default to collapsed state */
    width: var(--main-menu-collapsed-width);
    height: 100vh;
    background-color: var(--light-white);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    transition: width 0.3s ease;
    overflow-y: hidden; /* Hide scrollbar by default */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.main-menu:hover, .main-menu:focus-within {
    overflow-y: auto; /* Allow scroll on hover/focus if needed */
}

.main-menu.expanded {
    width: var(--main-menu-width);
}

.menu-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.welcome-text {
    color: var(--primary-navy);
    font-size: 16px;
    font-weight: 500;
    transition: opacity 0.3s ease;
    /* Hide by default since menu is collapsed by default */
    opacity: 0;
    display: none;
}

.main-menu.expanded .welcome-text {
    opacity: 1;
    display: block;
}

.menu-toggle {
    cursor: pointer;
    color: var(--primary-navy);
    font-size: 20px;
}

.logo-container {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    height: 100px;
    overflow: hidden;
}

.logo-container img {
    max-width: 100%;
    max-height: 60px;
    height: auto;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.main-menu.collapsed .logo-container {
    padding: 10px;
}

.main-menu.collapsed .logo-container img {
    transform: scale(0.8);
    opacity: 0.9;
}

.menu-section {
    margin: 20px 0;
}

.section-title {
    color: var(--primary-navy);
    opacity: 0;
    font-size: 12px;
    font-weight: 500;
    padding: 0 20px;
    margin-bottom: 10px;
    transition: opacity 0.3s ease;
    display: none;
}

.main-menu.expanded .section-title {
    opacity: 0.6;
    display: block;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: relative;
}

.menu-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.menu-item.active {
    background-color: var(--light-lavender);
}

.menu-item.active .menu-icon {
    color: var(--primary-purple);
}

.menu-item.active .menu-text {
    color: var(--primary-purple);
    font-weight: 500;
}

.menu-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: var(--primary-navy);
}

.menu-text {
    color: var(--primary-navy);
    font-size: 14px;
    transition: opacity 0.3s ease;
    /* Hide by default since menu is collapsed by default */
    opacity: 0;
    display: none;
}

.main-menu.expanded .menu-text {
    opacity: 1;
    display: block;
}

.dropdown-icon {
    margin-left: auto;
    font-size: 12px;
    color: var(--primary-navy);
    transition: transform 0.3s ease, opacity 0.3s ease;
    /* Hide by default since menu is collapsed by default */
    opacity: 0;
    display: none;
}

.main-menu.expanded .dropdown-icon {
    opacity: 1;
    display: block;
}

.menu-item.open .dropdown-icon {
    transform: rotate(180deg);
}

/* Submenu styles */
.submenu-container {
    width: var(--submenu-width);
    height: 100vh;
    background-color: var(--light-white);
    position: fixed;
    /* Since main menu is collapsed by default, position submenu accordingly */
    left: var(--main-menu-collapsed-width);
    top: 0;
    z-index: 99;
    transition: left 0.3s ease, width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
}

.main-menu.expanded + .submenu-container {
    left: var(--main-menu-width);
}

/* Hide these styles as we're not using collapsed submenu for now */
/*
.submenu-container.collapsed {
    width: var(--submenu-collapsed-width);
    left: var(--main-menu-width);
}

.main-menu.collapsed + .submenu-container.collapsed {
    left: var(--main-menu-collapsed-width);
}
*/

.submenu-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.submenu-toggle {
    cursor: pointer;
    color: var(--primary-navy);
    font-size: 20px;
}

.submenu-title {
    color: var(--primary-navy);
    font-size: 16px;
    font-weight: 500;
}

.submenu-section {
    margin: 20px 0;
}

.submenu-item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.submenu-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.submenu-item.active {
    background-color: var(--light-lavender);
}

.submenu-item.active .submenu-text {
    color: var(--primary-purple);
    font-weight: 500;
}

.submenu-icon {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-navy);
    margin-right: 15px;
}

.submenu-item.active .submenu-icon {
    background-color: var(--primary-purple);
}

.submenu-text {
    color: var(--primary-navy);
    font-size: 14px;
}

/* Responsive styles */
@media (max-width: 992px) {
    /* Main menu is already collapsed by default, so no need to change width */

    .main-menu.expanded {
        width: var(--main-menu-width);
    }

    .main-menu.expanded .welcome-text,
    .main-menu.expanded .section-title,
    .main-menu.expanded .menu-text,
    .main-menu.expanded .dropdown-icon {
        opacity: 1;
        display: block;
    }

    .main-menu.expanded + .submenu-container {
        left: var(--main-menu-width);
    }
}

@media (max-width: 768px) {
    .main-menu {
        width: 0;
    }

    .submenu-container {
        left: 0;
        width: 0;
    }

    .main-menu.expanded {
        width: var(--main-menu-width);
    }

    .submenu-container.expanded {
        width: var(--submenu-width);
    }
}
