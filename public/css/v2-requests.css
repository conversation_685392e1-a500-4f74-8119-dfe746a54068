/* v2 Requests Page Styles - Figma inspired */

.v2-requests-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: #fff;
  border-radius: 0 10px 10px 0;
  padding: 0;
  max-width: 1242px;
  margin: 0 auto;
}

.v2-requests-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 25px 30px;
  border-bottom: 2px solid #F5F6F7;
  background: #fff;
}

.v2-requests-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 24px;
  color: #5144A1;
}

.v2-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  border-radius: 8px;
  text-decoration: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.v2-btn-primary {
  background: #5144A1;
  color: #fff;
  border: none;
}
.v2-btn-primary:hover {
  background: #3d3380;
}
.v2-btn-outline {
  background: #fff;
  color: #5144A1;
  border: 1px solid #5144A1;
  margin-top: 16px;
}
.v2-btn-outline:hover {
  background: #f5f6f7;
}

.v2-requests-list {
  width: 100%;
  padding: 36px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.v2-request-card {
  background: #fff;
  border: 2px solid #F5F6F7;
  box-shadow: 0 2px 5px rgba(38, 51, 77, 0.03);
  border-radius: 5px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 740px;
  width: 100%;
}

.v2-request-card-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
}
.v2-request-avatar img {
  width: 50px;
  height: 50px;
  border-radius: 5px;
  object-fit: cover;
}
.v2-request-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}
.v2-request-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 20px;
  color: #4D5E80;
}
.v2-request-by {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #8347CC;
  display: flex;
  gap: 8px;
  align-items: center;
}
.v2-request-author {
  color: #4D5E80;
  font-weight: 700;
}
.v2-request-group {
  color: #4D5E80;
  font-weight: 400;
}
.v2-request-time {
  color: #7D8FB3;
  font-weight: 500;
  font-size: 14px;
}
.v2-request-fav {
  margin-left: auto;
  color: #9ACB48;
  font-size: 24px;
  cursor: pointer;
}

.v2-request-card-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.v2-request-amount {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 16px;
  color: #7D8FB3;
  margin-bottom: 8px;
}
.v2-request-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #4D5E80;
  margin-bottom: 8px;
}
.v2-request-actions {
  display: flex;
  flex-direction: row;
  gap: 15px;
  margin-bottom: 8px;
}
.v2-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #F7F8FD;
  background: #fff;
  color: #7D8FB3;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.v2-action-btn.active, .v2-action-btn:hover {
  background: rgba(154, 203, 72, 0.05);
  color: #9ACB48;
  border: 1px solid #9ACB48;
}
.v2-action-concur.active, .v2-action-concur:hover {
  background: rgba(154, 203, 72, 0.05);
  color: #9ACB48;
  border: 1px solid #9ACB48;
}

.v2-request-comments {
  background: rgba(43, 62, 80, 0.05);
  border-radius: 5px;
  padding: 20px;
  margin-top: 20px;
  width: 100%;
  max-width: 739px;
}
.v2-comment-form {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  background: #fff;
  border: 2px solid #F5F6F7;
  border-radius: 5px;
  padding: 10px 20px;
  margin-bottom: 15px;
}
.v2-comment-input {
  flex: 1;
  border: none;
  outline: none;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #7D8FB3;
  background: transparent;
}
.v2-comment-send {
  background: #5144A1;
  color: #fff;
  border: none;
  border-radius: 5px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.v2-comment-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 10px;
}
.v2-comment-avatar img {
  width: 30px;
  height: 30px;
  border-radius: 5px;
  object-fit: cover;
}
.v2-comment-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.v2-comment-author {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 12px;
  color: #4D5E80;
}
.v2-comment-time {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #7D8FB3;
}
.v2-comment-text {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #4D5E80;
}

.v2-empty-state {
  text-align: center;
  color: #7D8FB3;
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  margin: 60px auto;
}

.v2-pagination {
  margin-top: 32px;
  display: flex;
  justify-content: center;
}

.v2-requests-topbar {
  width: 100vw;
  max-width: 100%;
  background: #0D0D0D;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 32px 0 32px 40px;
  gap: 32px;
  border-radius: 0 10px 0 0;
  min-height: 80px;
  position: relative;
}
.v2-requests-tabs {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}
.v2-requests-tab {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 22px;
  color: #5144A1;
  text-decoration: none;
  opacity: 0.7;
  transition: color 0.2s, opacity 0.2s;
}
.v2-requests-tab.active {
  color: #7B61FF;
  opacity: 1;
}
.v2-requests-tab-sep {
  color: #C3CAD9;
  font-size: 22px;
  margin: 0 8px;
  opacity: 0.5;
}
.v2-requests-topbar .v2-btn {
  margin-left: auto;
  margin-right: 40px;
  font-size: 18px;
  padding: 12px 32px;
  background: #7B61FF;
  color: #fff;
  border-radius: 8px;
  font-weight: 700;
  box-shadow: none;
  border: none;
}
.v2-requests-topbar .v2-btn:hover {
  background: #5144A1;
}
.v2-requests-topbar .v2-btn.v2-btn-primary {
  background: #5144A1;
  border-radius: 8px;
  border: none;
  color: #fff;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 18px;
  width: 151px;
  height: 38px;
  min-width: 151px;
  min-height: 38px;
  max-width: 151px;
  max-height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 14px;
  text-decoration: none;
  box-shadow: none;
  padding: 0;
  margin-left: auto;
  margin-right: 40px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.v2-requests-topbar .v2-btn.v2-btn-primary:hover,
.v2-requests-topbar .v2-btn.v2-btn-primary:focus {
  background: #3d3380;
  color: #fff;
  text-decoration: none;
  outline: none;
}

@media (max-width: 900px) {
  .v2-requests-container, .v2-requests-list, .v2-request-card {
    max-width: 100%;
    padding: 12px;
  }
  .v2-request-card {
    padding: 12px;
  }
  .v2-requests-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 12px;
  }
  .v2-requests-topbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px 0 20px 12px;
  }
  .v2-requests-topbar .v2-btn {
    margin: 16px 0 0 0;
    width: 100%;
    font-size: 16px;
    padding: 10px 20px;
  }
  .v2-requests-topbar .v2-btn.v2-btn-primary {
    width: 100%;
    min-width: 0;
    max-width: 100%;
    margin: 16px 0 0 0;
    font-size: 16px;
    padding: 10px 20px;
  }
}

@media (max-width: 600px) {
  .v2-requests-header, .v2-requests-list, .v2-request-card, .v2-request-comments {
    padding: 8px !important;
  }
  .v2-request-card {
    gap: 8px;
  }
  .v2-request-card-header {
    gap: 8px;
  }
  .v2-request-title {
    font-size: 16px;
  }
  .v2-request-amount, .v2-request-description {
    font-size: 14px;
  }
  .v2-btn, .v2-btn-primary, .v2-btn-outline {
    font-size: 12px;
    padding: 8px 12px;
  }
  .v2-requests-topbar {
    padding: 12px 0 12px 4px;
    min-height: 60px;
  }
  .v2-requests-tabs {
    gap: 8px;
  }
  .v2-requests-tab, .v2-requests-tab-sep {
    font-size: 16px;
  }
  .v2-requests-topbar .v2-btn {
    font-size: 14px;
    padding: 8px 12px;
    margin-right: 8px;
  }
  .v2-requests-topbar .v2-btn.v2-btn-primary {
    font-size: 14px;
    padding: 8px 12px;
    margin-right: 8px;
    height: 38px;
    min-height: 38px;
    max-height: 38px;
  }
} 