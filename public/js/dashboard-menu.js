/**
 * Dashboard Menu JavaScript
 * Handles the collapsible main menu and submenu functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const mainMenu = document.querySelector('.main-menu');
    const submenuContainer = document.querySelector('.submenu-container');
    const contentWrapper = document.querySelector('.content-wrapper');
    const menuToggle = document.querySelector('.menu-toggle');
    const submenuToggle = document.querySelector('.submenu-toggle');
    const menuItems = document.querySelectorAll('.menu-item');

    // Toggle main menu
    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            mainMenu.classList.toggle('expanded');

            // For mobile
            if (window.innerWidth <= 768) {
                mainMenu.classList.toggle('expanded');
            }
        });
    }

    // Hide submenu toggle functionality as requested
    if (submenuToggle) {
        submenuToggle.style.display = 'none';
        /*
        submenuToggle.addEventListener('click', function() {
            submenuContainer.classList.toggle('collapsed');

            // For mobile
            if (window.innerWidth <= 768) {
                submenuContainer.classList.toggle('expanded');
            }
        });
        */
    }

    // Toggle dropdown menus
    menuItems.forEach(item => {
        const dropdown = item.querySelector('.dropdown-icon');
        if (dropdown) {
            item.addEventListener('click', function() {
                // Toggle the open class on the clicked item
                this.classList.toggle('open');

                // Find the next submenu
                const submenu = this.nextElementSibling;
                if (submenu && submenu.classList.contains('submenu')) {
                    // Toggle the submenu visibility
                    if (submenu.style.maxHeight) {
                        submenu.style.maxHeight = null;
                        submenu.style.opacity = '0';
                    } else {
                        submenu.style.maxHeight = submenu.scrollHeight + 'px';
                        submenu.style.opacity = '1';
                    }
                }
            });
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            mainMenu.classList.remove('expanded');
            submenuContainer.classList.remove('expanded');
        }
    });

    // Initialize menu state based on screen size
    if (window.innerWidth <= 768) {
        mainMenu.classList.remove('expanded');
    }

    // Close menus when clicking outside on mobile
    document.addEventListener('click', function(event) {
        if (window.innerWidth <= 768) {
            // Check if click is outside the main menu
            if (!mainMenu.contains(event.target) && !menuToggle.contains(event.target)) {
                mainMenu.classList.remove('expanded');
            }

            // Check if click is outside the submenu
            if (!submenuContainer.contains(event.target)) {
                submenuContainer.classList.remove('expanded');
            }
        }
    });
});
